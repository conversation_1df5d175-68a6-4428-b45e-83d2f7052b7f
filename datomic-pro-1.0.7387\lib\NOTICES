
----------------------------------------------------------------------------------------------------
SLF4J

Copyright (c) 2004-2011 QOS.ch
 All rights reserved.

 Permission is hereby granted, free  of charge, to any person obtaining
 a  copy  of this  software  and  associated  documentation files  (the
 "Software"), to  deal in  the Software without  restriction, including
 without limitation  the rights to  use, copy, modify,  merge, publish,
 distribute,  sublicense, and/or sell  copies of  the Software,  and to
 permit persons to whom the Software  is furnished to do so, subject to
 the following conditions:
 
 The  above  copyright  notice  and  this permission  notice  shall  be
 included in all copies or substantial portions of the Software.
 
 THE  SOFTWARE IS  PROVIDED  "AS  IS", WITHOUT  WARRANTY  OF ANY  KIND,
 EXPRESS OR  IMPLIED, INCLUDING  BUT NOT LIMITED  TO THE  WARRANTIES OF
 MERCHANTABILITY,    FITNESS    FOR    A   PARTICULAR    PURPOSE    AND
 NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 OF CONTRACT, TORT OR OTHERWISE,  ARISING FROM, OUT OF OR IN CONNECTION
 WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

----------------------------------------------------------------------------------------------------
ActiveMQ Artemis
License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
AWS SDK for Java
Copyright 2010-2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.

This product includes software developed by
Amazon Technologies, Inc (http://www.amazon.com/).

**********************
THIRD PARTY COMPONENTS
**********************
This software includes third party software subject to the following copyrights:
- XML parsing and utility functions from JetS3t - Copyright 2006-2009 James Murty.
- JSON parsing and utility functions from JSON.org - Copyright 2002 JSON.org.

License: licenses/aws-sdk-for-LICENSE.txt

----------------------------------------------------------------------------------------------------
BeanShell
(BeanShell is not used by Datomic but is included for backwards
compatibility with tutorials and documentation that use BeanShell scripts.)
/*****************************************************************************
 *                                                                           *
 *  This is BeanShell2, a fork of beanshell found at                         *
 *        http://www.beanshell.org/                                          *
 *                                                                           *
 *  BeanShell2 is hosted at Google-Code at                                   *
 *        http://code.google.com/p/beanshell2                                *
 *                                                                           *
 *  BeanShell2 is licensed under GNU Lesser GPL v3                           *
 *  See http://www.gnu.org/licenses/lgpl.html                                *
 *                                                                           *
/*****************************************************************************

----------------------------------------------------------------------------------------------------
Clojure and Clojure Contrib Libraries
  (complete list at https://github.com/clojure)
Copyright (c) Rich Hickey. All rights reserved.
License EPL 1.0, see file licenses/epl-v10.html

----------------------------------------------------------------------------------------------------
Apache Commons Codec
Copyright 2002-2009 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

--------------------------------------------------------------------------------
src/test/org/apache/commons/codec/language/DoubleMetaphoneTest.java contains 
test data from http://aspell.sourceforge.net/test/batch0.tab.

Copyright (C) 2002 Kevin Atkinson (<EMAIL>). Verbatim copying
and distribution of this entire article is permitted in any medium,
provided this notice is preserved.
--------------------------------------------------------------------------------

License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Janino

Janino - An embedded Java[TM] compiler

Copyright (c) 2001-2010, Arno Unkrig
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

   1. Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
   2. Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials
      provided with the distribution.
   3. The name of the author may not be used to endorse or promote
      products derived from this software without specific prior
      written permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.


----------------------------------------------------------------------------------------------------
Apache Commons FileUpload
Copyright 2002-2008 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Apache Jakarta HttpClient
Copyright 1999-2007 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Apache Commons IO
Copyright 2001-2008 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Apache Commons Pool
Copyright 2001-2011 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Guava
License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
H2
License: licenses/epl-v10.html

----------------------------------------------------------------------------------------------------
Apache HttpComponents HttpClient
Copyright 1999-2011 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

This project contains annotations derived from JCIP-ANNOTATIONS
Copyright (c) 2005 Brian Goetz and Tim Peierls. See http://www.jcip.net

License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Apache HttpComponents HttpCore
Copyright 2005-2011 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Jackson Core
This product currently only contains code developed by authors
of specific components, as identified by the source code files;
if such notes are missing files have been created by
Tatu Saloranta.

For additional credits (generally to people who reported problems)
see CREDITS file.

License: licenses/APACHE-2.0.txt


----------------------------------------------------------------------------------------------------
Java XMLBuilder
License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
java.classpath
License: licenses/epl-v10.html

----------------------------------------------------------------------------------------------------
JBoss Logging
License: licenses/lgpl-2.1.txt
Datomic does not use JBoss logging, and includes it only so that
ActiveMQ Artemis will load correctly.

----------------------------------------------------------------------------------------------------
JetS3t
License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Jetty
License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
JLine

Copyright (c) 2002-2006, Marc Prud'hommeaux <<EMAIL>>
All rights reserved.

Redistribution and use in source and binary forms, with or
without modification, are permitted provided that the following
conditions are met:

Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.

Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer
in the documentation and/or other materials provided with
the distribution.

Neither the name of JLine nor the names of its contributors
may be used to endorse or promote products derived from this
software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING,
BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY,
OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
OF THE POSSIBILITY OF SUCH DAMAGE.



----------------------------------------------------------------------------------------------------
logback-classic
License: licenses/epl-v10.html

----------------------------------------------------------------------------------------------------
logback-core
License: licenses/epl-v10.html

----------------------------------------------------------------------------------------------------
Apache Lucene
Copyright 2011 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

The snowball stemmers in
  contrib/analyzers/common/src/java/net/sf/snowball
were developed by Martin Porter and Richard Boulton.
The snowball stopword lists in
  contrib/analyzers/common/src/resources/org/apache/lucene/analysis/snowball
were developed by Martin Porter and Richard Boulton.
The full snowball package is available from
  http://snowball.tartarus.org/

The KStem stemmer in
  common/src/org/apache/lucene/analysis/en
was developed by Bob Krovetz and Sergio Guzman-Lara (CIIR-UMass Amherst)
under the BSD-license.

The Arabic,Persian,Romanian,Bulgarian, and Hindi analyzers (contrib/analyzers) come with a default
stopword list that is BSD-licensed created by Jacques Savoy.  These files reside in:
contrib/analyzers/common/src/resources/org/apache/lucene/analysis/ar/stopwords.txt,
contrib/analyzers/common/src/resources/org/apache/lucene/analysis/fa/stopwords.txt,
contrib/analyzers/common/src/resources/org/apache/lucene/analysis/ro/stopwords.txt,
contrib/analyzers/common/src/resources/org/apache/lucene/analysis/bg/stopwords.txt,
contrib/analyzers/common/src/resources/org/apache/lucene/analysis/hi/stopwords.txt
See http://members.unine.ch/jacques.savoy/clef/index.html.

The German,Spanish,Finnish,French,Hungarian,Italian,Portuguese,Russian and Swedish light stemmers
(common) are based on BSD-licensed reference implementations created by Jacques Savoy and
Ljiljana Dolamic. These files reside in:
contrib/analyzers/common/src/java/org/apache/lucene/analysis/de/GermanLightStemmer.java
contrib/analyzers/common/src/java/org/apache/lucene/analysis/de/GermanMinimalStemmer.java
contrib/analyzers/common/src/java/org/apache/lucene/analysis/es/SpanishLightStemmer.java
contrib/analyzers/common/src/java/org/apache/lucene/analysis/fi/FinnishLightStemmer.java
contrib/analyzers/common/src/java/org/apache/lucene/analysis/fr/FrenchLightStemmer.java
contrib/analyzers/common/src/java/org/apache/lucene/analysis/fr/FrenchMinimalStemmer.java
contrib/analyzers/common/src/java/org/apache/lucene/analysis/hu/HungarianLightStemmer.java
contrib/analyzers/common/src/java/org/apache/lucene/analysis/it/ItalianLightStemmer.java
contrib/analyzers/common/src/java/org/apache/lucene/analysis/pt/PortugueseLightStemmer.java
contrib/analyzers/common/src/java/org/apache/lucene/analysis/ru/RussianLightStemmer.java
contrib/analyzers/common/src/java/org/apache/lucene/analysis/sv/SwedishLightStemmer.java

The Stempel analyzer (contrib/analyzers) includes BSD-licensed software developed 
by the Egothor project http://egothor.sf.net/, created by Leo Galambos, Martin Kvapil,
and Edmond Nolan.

The Polish analyzer (contrib/analyzers) comes with a default
stopword list that is BSD-licensed created by the Carrot2 project. The file resides
in contrib/analyzers/stempel/src/resources/org/apache/lucene/analysis/pl/stopwords.txt.
See http://project.carrot2.org/license.html.

Includes lib/servlet-api-2.4.jar from  Apache Tomcat
Includes lib/ant-1.7.1.jar and lib/ant-junit-1.7.1.jar from Apache Ant
Includes contrib/queries/lib/jakarta-regexp-1.4.jar from Apache Jakarta Regexp
Includes software from other Apache Software Foundation projects,
including, but not limited to:
 - Commons Beanutils (contrib/benchmark/lib/commons-beanutils-1.7.0.jar)
 - Commons Collections (contrib/benchmark/lib/commons-collections-3.1.jar)
 - Commons Compress (contrib/benchmark/lib/commons-compress-1.0.jar)
 - Commons Digester (contrib/benchmark/lib/commons-digester-1.7.jar)
 - Commons Logging (contrib/benchmark/lib/commons-logging-1.0.4.jar)
 - Xerces (contrib/benchmark/lib/xercesImpl-2.9.1-patched-XERCESJ-1257.jar)

The SmartChineseAnalyzer source code (under contrib/analyzers) was
provided by Xiaoping Gao and copyright 2009 by www.imdict.net.

ICU4J, (under contrib/icu) is licensed under an MIT styles license
(contrib/icu/lib/ICU-LICENSE.txt) and Copyright (c) 1995-2008 
International Business Machines Corporation and others

Some files (contrib/analyzers/common/src/test/.../WordBreakTestUnicode_*.java
and data files under contrib/icu/src/data/) are derived from Unicode data such
as the Unicode Character Database. See http://unicode.org/copyright.html for more
details.

The class org.apache.lucene.SorterTemplate was inspired by CGLIB's class with
the same name. The implementation part is mainly done using pre-existing
Lucene sorting code. In-place stable mergesort was borrowed from CGLIB,
which is Apache-licensed.

The Google Code Prettify is Apache License 2.0.
See http://code.google.com/p/google-code-prettify/

JUnit (under lib/junit-4.7.jar) is licensed under the Common Public License v. 1.0
See http://junit.sourceforge.net/cpl-v10.html

JLine (under contrib/lucli/lib/jline.jar) is licensed under the BSD License.
See http://jline.sourceforge.net/

This product includes code (JaspellTernarySearchTrie) from Java Spelling Checking Package (jaspell): http://jaspell.sourceforge.net/
License: The BSD License (http://www.opensource.org/licenses/bsd-license.php)


License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------

                            The Netty Project
                            =================

Please visit the Netty web site for more information:

  * http://www.jboss.org/netty/

Copyright 2009 Red Hat, Inc.

Red Hat licenses this product to you under the Apache License, version 2.0 (the
"License"); you may not use this product except in compliance with the License.
You may obtain a copy of the License at:

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software distributed
under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
CONDITIONS OF ANY KIND, either express or implied.  See the License for the
specific language governing permissions and limitations under the License.

Also, please refer to each LICENSE.<component>.txt file, which is located in
the 'license' directory of the distribution file, for the license terms of the
components that this product depends on.

-------------------------------------------------------------------------------
This product contains the extensions to Java Collections Framework which has
been derived from the works by JSR-166 EG, Doug Lea, and Jason T. Greene:

  * LICENSE:
    * license/LICENSE.jsr166y.txt (Public Domain)
  * HOMEPAGE:
    * http://gee.cs.oswego.edu/cgi-bin/viewcvs.cgi/jsr166/
    * http://viewvc.jboss.org/cgi-bin/viewvc.cgi/jbosscache/experimental/jsr166/

This product contains a modified version of Robert Harder's Public Domain
Base64 Encoder and Decoder, which can be obtained at:

  * LICENSE:
    * license/LICENSE.base64.txt (Public Domain)
  * HOMEPAGE:
    * http://iharder.sourceforge.net/current/java/base64/

This product contains a modified version of 'JZlib', a re-implementation of
zlib in pure Java, which can be obtained at:

  * LICENSE:
    * license/LICENSE.jzlib.txt (BSD Style License)
  * HOMEPAGE:
    * http://www.jcraft.com/jzlib/

This product optionally depends on 'Protocol Buffers', Google's data
interchange format, which can be obtained at:

  * LICENSE:
    * license/LICENSE.protobuf.txt (New BSD License)
  * HOMEPAGE:
    * http://code.google.com/p/protobuf/

This product optionally depends on 'SLF4J', a simple logging facade for Java,
which can be obtained at:

  * LICENSE:
    * license/LICENSE.slf4j.txt (MIT License)
  * HOMEPAGE:
    * http://www.slf4j.org/

This product optionally depends on 'Apache Commons Logging', a logging
framework, which can be obtained at:

  * LICENSE:
    * license/LICENSE.commons-logging.txt (Apache License 2.0)
  * HOMEPAGE:
    * http://commons.apache.org/logging/

This product optionally depends on 'Apache Log4J', a logging framework,
which can be obtained at:

  * LICENSE:
    * license/LICENSE.log4j.txt (Apache License 2.0)
  * HOMEPAGE:
    * http://logging.apache.org/log4j/

This product optionally depends on 'JBoss Logging', a logging framework,
which can be obtained at:

  * LICENSE:
    * license/LICENSE.jboss-logging.txt (GNU LGPL 2.1)
  * HOMEPAGE:
    * http://anonsvn.jboss.org/repos/common/common-logging-spi/

This product optionally depends on 'Apache Felix', an open source OSGi
framework implementation, which can be obtained at:

  * LICENSE:
    * license/LICENSE.felix.txt (Apache License 2.0)
  * HOMEPAGE:
    * http://felix.apache.org/


License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Postgresql JDBC Driver

The PostgreSQL JDBC driver is distributed under the BSD license, same as the server. The simplest explanation of the licensing terms is that you can do whatever you want with the product and source code as long as you don't claim you wrote it or sue us. You should give it a read though, it's only half a page.

Copyright (c) 1997-2010, PostgreSQL Global Development Group
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.
3. Neither the name of the PostgreSQL Global Development Group nor the names
   of its contributors may be used to endorse or promote products derived
   from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.



----------------------------------------------------------------------------------------------------
ring

Copyright (c) 2009-2010 Mark McGranaghan
 
Permission is hereby granted, free of charge, to any person
obtaining a copy of this software and associated documentation
files (the "Software"), to deal in the Software without
restriction, including without limitation the rights to use,
copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following
conditions:
 
The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.
 
THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
OTHER DEALINGS IN THE SOFTWARE.



----------------------------------------------------------------------------------------------------
Servlet API
License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
spymemcached

Copyright (c) 2006-2009  Dustin Sallings
Copyright (c) 2009-2011  Couchbase, Inc.

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
of the Software, and to permit persons to whom the Software is furnished to do
so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

----------------------------------------------------------------------------------------------------
stax
License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Apache Tomcat JDBC Pool
Copyright 2008-2012 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
Apache Tomcat
Copyright 1999-2012 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

License: licenses/APACHE-2.0.txt

----------------------------------------------------------------------------------------------------
tools.namespace
License: licenses/epl-v10.html

----------------------------------------------------------------------------------------------------
Woodstox

This product currently only contains code developed by authors
of specific components, as identified by the source code files.

Since product implements StAX API, it has dependencies to StAX API
classes.

For additional credits (generally to people who reported problems)
see CREDITS file.

License: licenses/APACHE-2.0.txt
