## 1. 前置准备

### 先决条件

- 安装 Python 3.10 或更高版本
- 拥有 TickTick 账户
- 注册 TickTick 应用程序以获取 API 凭据：
  - 访问 [TickTick OpenAPI Documentation](https://developer.ticktick.com/docs#/openapi) 并登录
  - 点击右上角的 "Manage Apps"
  - 点击 "+App Name" 按钮注册新应用
  - 创建后编辑应用详情，记录生成的 `Client ID` 和 `Client Secret`
  - 设置 `OAuth Redirect URL`（如：http://localhost:8080/redirect）

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `TICKTICK_CLIENT_ID`: 您的 TickTick 客户端 ID
- `TICKTICK_CLIENT_SECRET`: 您的 TickTick 客户端密钥
- `TICKTICK_REDIRECT_URI`: OAuth 重定向 URI（必须与应用设置中的完全匹配）
- `TICKTICK_USERNAME`: 您的 TickTick 登录邮箱
- `TICKTICK_PASSWORD`: 您的 TickTick 登录密码

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

**注意：** 首次运行时需要完成 OAuth2 认证流程，系统会自动打开浏览器或提供认证链接。
