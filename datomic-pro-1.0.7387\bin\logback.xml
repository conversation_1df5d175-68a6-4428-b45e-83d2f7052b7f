<configuration>

  <!-- prevent per-message overhead for jul logging calls, e.g. Horne<PERSON> -->
  <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
    <resetJUL>true</resetJUL>
  </contextListener>

  <appender name="MAIN" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${DATOMIC_LOG_DIR:-log}/%d{yyyy-MM-dd}.log</fileNamePattern>
      <maxHistory>72</maxHistory> 
    </rollingPolicy>
    <prudent>true</prudent> <!-- multi jvm safe, slower -->
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %-10contextName %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>

  <logger name="datomic.cast2slf4j" level="DEBUG"/>

  <!-- uncomment to log storage access -->
  <!-- <logger name="datomic.kv-cluster" level="DEBUG"/> -->

  <!-- uncomment to log transactor heartbeat -->
  <!-- <logger name="datomic.lifecycle" level="DEBUG"/> -->

  <!-- uncomment to log transactions (transactor side) -->
  <!-- <logger name="datomic.transaction" level="DEBUG"/> -->

  <!-- uncomment to log transactions (peer side) -->
  <!-- <logger name="datomic.peer" level="DEBUG"/> -->

  <!-- uncomment to log the transactor log -->
  <!-- <logger name="datomic.log" level="DEBUG"/> -->

  <!-- uncomment to log peer connection to transactor -->
  <!-- <logger name="datomic.connector" level="DEBUG"/> -->

  <!-- uncomment to log storage gc -->
  <!-- <logger name="datomic.garbage" level="DEBUG"/> -->

  <!-- uncomment to log indexing jobs -->
  <!-- <logger name="datomic.index" level="DEBUG"/> -->

  <!-- these namespsaces create a ton of log noise -->
  <logger name="org.apache.activemq.audit" level="WARN"/>
  <logger name="httpclient" level="INFO"/>
  <logger name="org.apache.commons.httpclient" level="INFO"/>
  <logger name="org.apache.http" level="INFO"/>
  <logger name="org.jets3t" level="INFO"/>
  <logger name="com.amazonaws" level="INFO"/>
  <logger name="com.amazonaws.request" level="WARN"/>
  <logger name="sun.rmi" level="INFO"/> 
  <logger name="datomic.spy.memcached" level="INFO"/>
  <logger name="com.ning.http.client.providers.netty" level="INFO"/>
  <logger name="org.eclipse.jetty" level="INFO"/>
  <logger name="org.hornetq.core.client.impl" level="INFO"/>
  <logger name="org.apache.tomcat.jdbc.pool" level="INFO"/>

  <logger name="datomic.cast2slf4j" level="DEBUG"/>

  <root level="info">
    <appender-ref ref="MAIN"/>
  </root>
</configuration>
