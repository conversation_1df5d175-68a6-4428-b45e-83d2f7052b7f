src-build;.;C:\Users\<USER>\.gitlibs\libs\io.github.clojure\tools.build\8e78bccc35116f6b6fc0bf0c125dba8b8db8da6b\src\main\clojure;C:\Users\<USER>\.gitlibs\libs\io.github.clojure\tools.build\8e78bccc35116f6b6fc0bf0c125dba8b8db8da6b\src\main\resources;C:\Users\<USER>\.m2\repository\org\clojure\clojure\1.12.1\clojure-1.12.1.jar;C:\Users\<USER>\.m2\repository\org\clojure\tools.deps\0.18.1354\tools.deps-0.18.1354.jar;C:\Users\<USER>\.m2\repository\org\clojure\tools.namespace\1.4.4\tools.namespace-1.4.4.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-nop\1.7.36\slf4j-nop-1.7.36.jar;C:\Users\<USER>\.m2\repository\org\clojure\core.specs.alpha\0.4.74\core.specs.alpha-0.4.74.jar;C:\Users\<USER>\.m2\repository\org\clojure\spec.alpha\0.5.238\spec.alpha-0.5.238.jar;C:\Users\<USER>\.m2\repository\com\cognitect\aws\api\0.8.612\api-0.8.612.jar;C:\Users\<USER>\.m2\repository\com\cognitect\aws\endpoints\1.1.12.321\endpoints-1.1.12.321.jar;C:\Users\<USER>\.m2\repository\com\cognitect\aws\s3\822.2.1145.0\s3-822.2.1145.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-android\guava-31.1-android.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-core\3.8.6\maven-core-3.8.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-resolver-provider\3.8.6\maven-resolver-provider-3.8.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-api\1.8.2\maven-resolver-api-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-connector-basic\1.8.2\maven-resolver-connector-basic-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-impl\1.8.2\maven-resolver-impl-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-spi\1.8.2\maven-resolver-spi-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-transport-file\1.8.2\maven-resolver-transport-file-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-transport-http\1.8.2\maven-resolver-transport-http-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-util\1.8.2\maven-resolver-util-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\clojure\data.xml\0.2.0-alpha8\data.xml-0.2.0-alpha8.jar;C:\Users\<USER>\.m2\repository\org\clojure\tools.cli\1.0.214\tools.cli-1.0.214.jar;C:\Users\<USER>\.m2\repository\org\clojure\tools.gitlibs\2.5.197\tools.gitlibs-2.5.197.jar;C:\Users\<USER>\.m2\repository\org\clojure\java.classpath\1.0.0\java.classpath-1.0.0.jar;C:\Users\<USER>\.m2\repository\org\clojure\tools.reader\1.3.6\tools.reader-1.3.6.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\com\cognitect\http-client\1.0.115\http-client-1.0.115.jar;C:\Users\<USER>\.m2\repository\org\clojure\core.async\1.6.673\core.async-1.6.673.jar;C:\Users\<USER>\.m2\repository\org\clojure\data.json\2.4.0\data.json-2.4.0.jar;C:\Users\<USER>\.m2\repository\org\clojure\tools.logging\1.2.4\tools.logging-1.2.4.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\inject\guice\4.2.2\guice-4.2.2-no_aop.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\3.8.6\maven-artifact-3.8.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-builder-support\3.8.6\maven-builder-support-3.8.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\3.8.6\maven-model-3.8.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model-builder\3.8.6\maven-model-builder-3.8.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-plugin-api\3.8.6\maven-plugin-api-3.8.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\3.8.6\maven-repository-metadata-3.8.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-settings\3.8.6\maven-settings-3.8.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-settings-builder\3.8.6\maven-settings-builder-3.8.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\shared\maven-shared-utils\3.3.4\maven-shared-utils-3.3.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-classworlds\2.6.0\plexus-classworlds-2.6.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-component-annotations\2.1.0\plexus-component-annotations-2.1.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.26\plexus-interpolation-1.26.jar;C:\Users\<USER>\.m2\repository\org\eclipse\sisu\org.eclipse.sisu.inject\0.3.5\org.eclipse.sisu.inject-0.3.5.jar;C:\Users\<USER>\.m2\repository\org\eclipse\sisu\org.eclipse.sisu.plexus\0.3.5\org.eclipse.sisu.plexus-0.3.5.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-named-locks\1.8.2\maven-resolver-named-locks-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jcl-over-slf4j\1.7.36\jcl-over-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-client\9.4.48.v20220622\jetty-client-9.4.48.v20220622.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-http\9.4.48.v20220622\jetty-http-9.4.48.v20220622.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util\9.4.48.v20220622\jetty-util-9.4.48.v20220622.jar;C:\Users\<USER>\.m2\repository\org\clojure\tools.analyzer.jvm\1.2.2\tools.analyzer.jvm-1.2.2.jar;C:\Users\<USER>\.m2\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-sec-dispatcher\2.0\plexus-sec-dispatcher-2.0.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.11\commons-codec-1.11.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-io\9.4.48.v20220622\jetty-io-9.4.48.v20220622.jar;C:\Users\<USER>\.m2\repository\org\clojure\core.memoize\1.0.253\core.memoize-1.0.253.jar;C:\Users\<USER>\.m2\repository\org\clojure\tools.analyzer\1.1.0\tools.analyzer-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.2\asm-9.2.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-cipher\2.0\plexus-cipher-2.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\3.4.1\plexus-utils-3.4.1.jar;C:\Users\<USER>\.m2\repository\org\clojure\core.cache\1.0.225\core.cache-1.0.225.jar;C:\Users\<USER>\.m2\repository\org\clojure\data.priority-map\1.1.0\data.priority-map-1.1.0.jar