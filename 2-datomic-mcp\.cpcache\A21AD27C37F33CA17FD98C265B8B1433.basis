{:classpath {"C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-interpolation\\1.26\\plexus-interpolation-1.26.jar" {:lib-name org.codehaus.plexus/plexus-interpolation}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-settings\\3.8.6\\maven-settings-3.8.6.jar" {:lib-name org.apache.maven/maven-settings}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-transport-http\\1.8.2\\maven-resolver-transport-http-1.8.2.jar" {:lib-name org.apache.maven.resolver/maven-resolver-transport-http}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.cache\\1.0.225\\core.cache-1.0.225.jar" {:lib-name org.clojure/core.cache}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.namespace\\1.4.4\\tools.namespace-1.4.4.jar" {:lib-name org.clojure/tools.namespace}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.analyzer.jvm\\1.2.2\\tools.analyzer.jvm-1.2.2.jar" {:lib-name org.clojure/tools.analyzer.jvm}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpcore\\4.4.15\\httpcore-4.4.15.jar" {:lib-name org.apache.httpcomponents/httpcore}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.deps\\0.18.1354\\tools.deps-0.18.1354.jar" {:lib-name org.clojure/tools.deps}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.async\\1.6.673\\core.async-1.6.673.jar" {:lib-name org.clojure/core.async}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.cli\\1.0.214\\tools.cli-1.0.214.jar" {:lib-name org.clojure/tools.cli}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-impl\\1.8.2\\maven-resolver-impl-1.8.2.jar" {:lib-name org.apache.maven.resolver/maven-resolver-impl}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.analyzer\\1.1.0\\tools.analyzer-1.1.0.jar" {:lib-name org.clojure/tools.analyzer}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-repository-metadata\\3.8.6\\maven-repository-metadata-3.8.6.jar" {:lib-name org.apache.maven/maven-repository-metadata}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\java.classpath\\1.0.0\\java.classpath-1.0.0.jar" {:lib-name org.clojure/java.classpath}, "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\guava\\31.1-android\\guava-31.1-android.jar" {:lib-name com.google.guava/guava}, "C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\http-client\\1.0.115\\http-client-1.0.115.jar" {:lib-name com.cognitect/http-client}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\spec.alpha\\0.5.238\\spec.alpha-0.5.238.jar" {:lib-name org.clojure/spec.alpha}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-model\\3.8.6\\maven-model-3.8.6.jar" {:lib-name org.apache.maven/maven-model}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\clojure\\1.12.1\\clojure-1.12.1.jar" {:lib-name org.clojure/clojure}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.specs.alpha\\0.4.74\\core.specs.alpha-0.4.74.jar" {:lib-name org.clojure/core.specs.alpha}, "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\errorprone\\error_prone_annotations\\2.11.0\\error_prone_annotations-2.11.0.jar" {:lib-name com.google.errorprone/error_prone_annotations}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-builder-support\\3.8.6\\maven-builder-support-3.8.6.jar" {:lib-name org.apache.maven/maven-builder-support}, "C:\\Users\\<USER>\\.m2\\repository\\javax\\annotation\\javax.annotation-api\\1.3.2\\javax.annotation-api-1.3.2.jar" {:lib-name javax.annotation/javax.annotation-api}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.gitlibs\\2.5.197\\tools.gitlibs-2.5.197.jar" {:lib-name org.clojure/tools.gitlibs}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.reader\\1.3.6\\tools.reader-1.3.6.jar" {:lib-name org.clojure/tools.reader}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\data.json\\2.4.0\\data.json-2.4.0.jar" {:lib-name org.clojure/data.json}, "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-classworlds\\2.6.0\\plexus-classworlds-2.6.0.jar" {:lib-name org.codehaus.plexus/plexus-classworlds}, "C:\\Users\\<USER>\\.gitlibs\\libs\\io.github.clojure\\tools.build\\8e78bccc35116f6b6fc0bf0c125dba8b8db8da6b\\src\\main\\clojure" {:lib-name io.github.clojure/tools.build}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.memoize\\1.0.253\\core.memoize-1.0.253.jar" {:lib-name org.clojure/core.memoize}, "C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\aws\\endpoints\\1.1.12.321\\endpoints-1.1.12.321.jar" {:lib-name com.cognitect.aws/endpoints}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.logging\\1.2.4\\tools.logging-1.2.4.jar" {:lib-name org.clojure/tools.logging}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-util\\1.8.2\\maven-resolver-util-1.8.2.jar" {:lib-name org.apache.maven.resolver/maven-resolver-util}, "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\jcl-over-slf4j\\1.7.36\\jcl-over-slf4j-1.7.36.jar" {:lib-name org.slf4j/jcl-over-slf4j}, "C:\\Users\\<USER>\\.gitlibs\\libs\\io.github.clojure\\tools.build\\8e78bccc35116f6b6fc0bf0c125dba8b8db8da6b\\src\\main\\resources" {:lib-name io.github.clojure/tools.build}, "src-build" {:path-key :extra-paths}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-plugin-api\\3.8.6\\maven-plugin-api-3.8.6.jar" {:lib-name org.apache.maven/maven-plugin-api}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-artifact\\3.8.6\\maven-artifact-3.8.6.jar" {:lib-name org.apache.maven/maven-artifact}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-named-locks\\1.8.2\\maven-resolver-named-locks-1.8.2.jar" {:lib-name org.apache.maven.resolver/maven-resolver-named-locks}, "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\sisu\\org.eclipse.sisu.inject\\0.3.5\\org.eclipse.sisu.inject-0.3.5.jar" {:lib-name org.eclipse.sisu/org.eclipse.sisu.inject}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-core\\3.8.6\\maven-core-3.8.6.jar" {:lib-name org.apache.maven/maven-core}, "C:\\Users\\<USER>\\.m2\\repository\\org\\checkerframework\\checker-qual\\3.12.0\\checker-qual-3.12.0.jar" {:lib-name org.checkerframework/checker-qual}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-connector-basic\\1.8.2\\maven-resolver-connector-basic-1.8.2.jar" {:lib-name org.apache.maven.resolver/maven-resolver-connector-basic}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-model-builder\\3.8.6\\maven-model-builder-3.8.6.jar" {:lib-name org.apache.maven/maven-model-builder}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpclient\\4.5.13\\httpclient-4.5.13.jar" {:lib-name org.apache.httpcomponents/httpclient}, "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\failureaccess\\1.0.1\\failureaccess-1.0.1.jar" {:lib-name com.google.guava/failureaccess}, "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-client\\9.4.48.v20220622\\jetty-client-9.4.48.v20220622.jar" {:lib-name org.eclipse.jetty/jetty-client}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-api\\1.8.2\\maven-resolver-api-1.8.2.jar" {:lib-name org.apache.maven.resolver/maven-resolver-api}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-resolver-provider\\3.8.6\\maven-resolver-provider-3.8.6.jar" {:lib-name org.apache.maven/maven-resolver-provider}, "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-cipher\\2.0\\plexus-cipher-2.0.jar" {:lib-name org.codehaus.plexus/plexus-cipher}, "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-nop\\1.7.36\\slf4j-nop-1.7.36.jar" {:lib-name org.slf4j/slf4j-nop}, "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\sisu\\org.eclipse.sisu.plexus\\0.3.5\\org.eclipse.sisu.plexus-0.3.5.jar" {:lib-name org.eclipse.sisu/org.eclipse.sisu.plexus}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-settings-builder\\3.8.6\\maven-settings-builder-3.8.6.jar" {:lib-name org.apache.maven/maven-settings-builder}, "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-api\\1.7.36\\slf4j-api-1.7.36.jar" {:lib-name org.slf4j/slf4j-api}, "C:\\Users\\<USER>\\.m2\\repository\\commons-io\\commons-io\\2.11.0\\commons-io-2.11.0.jar" {:lib-name commons-io/commons-io}, "." {:path-key :paths}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\shared\\maven-shared-utils\\3.3.4\\maven-shared-utils-3.3.4.jar" {:lib-name org.apache.maven.shared/maven-shared-utils}, "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-io\\9.4.48.v20220622\\jetty-io-9.4.48.v20220622.jar" {:lib-name org.eclipse.jetty/jetty-io}, "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\code\\findbugs\\jsr305\\3.0.2\\jsr305-3.0.2.jar" {:lib-name com.google.code.findbugs/jsr305}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-spi\\1.8.2\\maven-resolver-spi-1.8.2.jar" {:lib-name org.apache.maven.resolver/maven-resolver-spi}, "C:\\Users\\<USER>\\.m2\\repository\\org\\ow2\\asm\\asm\\9.2\\asm-9.2.jar" {:lib-name org.ow2.asm/asm}, "C:\\Users\\<USER>\\.m2\\repository\\javax\\inject\\javax.inject\\1\\javax.inject-1.jar" {:lib-name javax.inject/javax.inject}, "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-sec-dispatcher\\2.0\\plexus-sec-dispatcher-2.0.jar" {:lib-name org.codehaus.plexus/plexus-sec-dispatcher}, "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\listenablefuture\\9999.0-empty-to-avoid-conflict-with-guava\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar" {:lib-name com.google.guava/listenablefuture}, "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-component-annotations\\2.1.0\\plexus-component-annotations-2.1.0.jar" {:lib-name org.codehaus.plexus/plexus-component-annotations}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\data.xml\\0.2.0-alpha8\\data.xml-0.2.0-alpha8.jar" {:lib-name org.clojure/data.xml}, "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-http\\9.4.48.v20220622\\jetty-http-9.4.48.v20220622.jar" {:lib-name org.eclipse.jetty/jetty-http}, "C:\\Users\\<USER>\\.m2\\repository\\aopalliance\\aopalliance\\1.0\\aopalliance-1.0.jar" {:lib-name aopalliance/aopalliance}, "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\inject\\guice\\4.2.2\\guice-4.2.2-no_aop.jar" {:lib-name com.google.inject/guice$no_aop}, "C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\aws\\api\\0.8.612\\api-0.8.612.jar" {:lib-name com.cognitect.aws/api}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-lang3\\3.12.0\\commons-lang3-3.12.0.jar" {:lib-name org.apache.commons/commons-lang3}, "C:\\Users\\<USER>\\.m2\\repository\\commons-codec\\commons-codec\\1.11\\commons-codec-1.11.jar" {:lib-name commons-codec/commons-codec}, "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\data.priority-map\\1.1.0\\data.priority-map-1.1.0.jar" {:lib-name org.clojure/data.priority-map}, "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\j2objc\\j2objc-annotations\\1.3\\j2objc-annotations-1.3.jar" {:lib-name com.google.j2objc/j2objc-annotations}, "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-utils\\3.4.1\\plexus-utils-3.4.1.jar" {:lib-name org.codehaus.plexus/plexus-utils}, "C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\aws\\s3\\822.2.1145.0\\s3-822.2.1145.0.jar" {:lib-name com.cognitect.aws/s3}, "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-transport-file\\1.8.2\\maven-resolver-transport-file-1.8.2.jar" {:lib-name org.apache.maven.resolver/maven-resolver-transport-file}, "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-util\\9.4.48.v20220622\\jetty-util-9.4.48.v20220622.jar" {:lib-name org.eclipse.jetty/jetty-util}}, :basis-config {:project "deps.edn", :user "C:\\Users\\<USER>\\.clojure\\deps.edn", :extra nil, :args {:replace-paths ["."], :replace-deps {}}, :aliases [:build]}, :mvn/repos {"central" {:url "https://repo1.maven.org/maven2/"}, "clojars" {:url "https://repo.clojars.org/"}}, :argmap {:extra-paths ["src-build"], :deps {io.github.clojure/tools.build {:git/tag "v0.9.6", :git/sha "8e78bcc"}}, :ns-default build, :replace-paths ["."], :replace-deps {}}, :paths ["."], :classpath-roots ["src-build" "." "C:\\Users\\<USER>\\.gitlibs\\libs\\io.github.clojure\\tools.build\\8e78bccc35116f6b6fc0bf0c125dba8b8db8da6b\\src\\main\\clojure" "C:\\Users\\<USER>\\.gitlibs\\libs\\io.github.clojure\\tools.build\\8e78bccc35116f6b6fc0bf0c125dba8b8db8da6b\\src\\main\\resources" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\clojure\\1.12.1\\clojure-1.12.1.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.deps\\0.18.1354\\tools.deps-0.18.1354.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.namespace\\1.4.4\\tools.namespace-1.4.4.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-nop\\1.7.36\\slf4j-nop-1.7.36.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.specs.alpha\\0.4.74\\core.specs.alpha-0.4.74.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\spec.alpha\\0.5.238\\spec.alpha-0.5.238.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\aws\\api\\0.8.612\\api-0.8.612.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\aws\\endpoints\\1.1.12.321\\endpoints-1.1.12.321.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\aws\\s3\\822.2.1145.0\\s3-822.2.1145.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\guava\\31.1-android\\guava-31.1-android.jar" "C:\\Users\\<USER>\\.m2\\repository\\commons-io\\commons-io\\2.11.0\\commons-io-2.11.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\javax\\inject\\javax.inject\\1\\javax.inject-1.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-core\\3.8.6\\maven-core-3.8.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-resolver-provider\\3.8.6\\maven-resolver-provider-3.8.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-api\\1.8.2\\maven-resolver-api-1.8.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-connector-basic\\1.8.2\\maven-resolver-connector-basic-1.8.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-impl\\1.8.2\\maven-resolver-impl-1.8.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-spi\\1.8.2\\maven-resolver-spi-1.8.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-transport-file\\1.8.2\\maven-resolver-transport-file-1.8.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-transport-http\\1.8.2\\maven-resolver-transport-http-1.8.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-util\\1.8.2\\maven-resolver-util-1.8.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\data.xml\\0.2.0-alpha8\\data.xml-0.2.0-alpha8.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.cli\\1.0.214\\tools.cli-1.0.214.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.gitlibs\\2.5.197\\tools.gitlibs-2.5.197.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\java.classpath\\1.0.0\\java.classpath-1.0.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.reader\\1.3.6\\tools.reader-1.3.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-api\\1.7.36\\slf4j-api-1.7.36.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\http-client\\1.0.115\\http-client-1.0.115.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.async\\1.6.673\\core.async-1.6.673.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\data.json\\2.4.0\\data.json-2.4.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.logging\\1.2.4\\tools.logging-1.2.4.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\code\\findbugs\\jsr305\\3.0.2\\jsr305-3.0.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\errorprone\\error_prone_annotations\\2.11.0\\error_prone_annotations-2.11.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\failureaccess\\1.0.1\\failureaccess-1.0.1.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\listenablefuture\\9999.0-empty-to-avoid-conflict-with-guava\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\j2objc\\j2objc-annotations\\1.3\\j2objc-annotations-1.3.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\checkerframework\\checker-qual\\3.12.0\\checker-qual-3.12.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\inject\\guice\\4.2.2\\guice-4.2.2-no_aop.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-artifact\\3.8.6\\maven-artifact-3.8.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-builder-support\\3.8.6\\maven-builder-support-3.8.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-model\\3.8.6\\maven-model-3.8.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-model-builder\\3.8.6\\maven-model-builder-3.8.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-plugin-api\\3.8.6\\maven-plugin-api-3.8.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-repository-metadata\\3.8.6\\maven-repository-metadata-3.8.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-settings\\3.8.6\\maven-settings-3.8.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-settings-builder\\3.8.6\\maven-settings-builder-3.8.6.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\shared\\maven-shared-utils\\3.3.4\\maven-shared-utils-3.3.4.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-classworlds\\2.6.0\\plexus-classworlds-2.6.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-component-annotations\\2.1.0\\plexus-component-annotations-2.1.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-interpolation\\1.26\\plexus-interpolation-1.26.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\sisu\\org.eclipse.sisu.inject\\0.3.5\\org.eclipse.sisu.inject-0.3.5.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\sisu\\org.eclipse.sisu.plexus\\0.3.5\\org.eclipse.sisu.plexus-0.3.5.jar" "C:\\Users\\<USER>\\.m2\\repository\\javax\\annotation\\javax.annotation-api\\1.3.2\\javax.annotation-api-1.3.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-lang3\\3.12.0\\commons-lang3-3.12.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-named-locks\\1.8.2\\maven-resolver-named-locks-1.8.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpclient\\4.5.13\\httpclient-4.5.13.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpcore\\4.4.15\\httpcore-4.4.15.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\jcl-over-slf4j\\1.7.36\\jcl-over-slf4j-1.7.36.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-client\\9.4.48.v20220622\\jetty-client-9.4.48.v20220622.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-http\\9.4.48.v20220622\\jetty-http-9.4.48.v20220622.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-util\\9.4.48.v20220622\\jetty-util-9.4.48.v20220622.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.analyzer.jvm\\1.2.2\\tools.analyzer.jvm-1.2.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\aopalliance\\aopalliance\\1.0\\aopalliance-1.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-sec-dispatcher\\2.0\\plexus-sec-dispatcher-2.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\commons-codec\\commons-codec\\1.11\\commons-codec-1.11.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-io\\9.4.48.v20220622\\jetty-io-9.4.48.v20220622.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.memoize\\1.0.253\\core.memoize-1.0.253.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.analyzer\\1.1.0\\tools.analyzer-1.1.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\ow2\\asm\\asm\\9.2\\asm-9.2.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-cipher\\2.0\\plexus-cipher-2.0.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-utils\\3.4.1\\plexus-utils-3.4.1.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.cache\\1.0.225\\core.cache-1.0.225.jar" "C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\data.priority-map\\1.1.0\\data.priority-map-1.1.0.jar"], :libs {javax.inject/javax.inject {:mvn/version "1", :deps/manifest :mvn, :dependents [org.clojure/tools.deps org.codehaus.plexus/plexus-sec-dispatcher org.codehaus.plexus/plexus-cipher org.apache.maven/maven-core com.google.inject/guice$no_aop org.apache.maven/maven-model-builder org.apache.maven/maven-settings-builder org.apache.maven/maven-resolver-provider], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-settings-builder org.codehaus.plexus/plexus-sec-dispatcher] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-settings-builder org.codehaus.plexus/plexus-sec-dispatcher org.codehaus.plexus/plexus-cipher] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core com.google.inject/guice$no_aop] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-resolver-provider org.apache.maven/maven-model-builder] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-settings-builder] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-resolver-provider]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\javax\\inject\\javax.inject\\1\\javax.inject-1.jar"]}, org.clojure/data.json {:mvn/version "2.4.0", :deps/manifest :mvn, :dependents [com.cognitect.aws/api], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\data.json\\2.4.0\\data.json-2.4.0.jar"]}, org.clojure/clojure {:mvn/version "1.12.1", :deps/manifest :mvn, :parents #{[]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\clojure\\1.12.1\\clojure-1.12.1.jar"]}, commons-codec/commons-codec {:mvn/version "1.11", :deps/manifest :mvn, :dependents [org.apache.httpcomponents/httpclient], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-http org.apache.httpcomponents/httpclient]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\commons-codec\\commons-codec\\1.11\\commons-codec-1.11.jar"]}, com.cognitect.aws/api {:mvn/version "0.8.612", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\aws\\api\\0.8.612\\api-0.8.612.jar"]}, org.clojure/tools.analyzer {:mvn/version "1.1.0", :deps/manifest :mvn, :dependents [org.clojure/tools.analyzer.jvm], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api org.clojure/core.async org.clojure/tools.analyzer.jvm]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.analyzer\\1.1.0\\tools.analyzer-1.1.0.jar"]}, org.codehaus.plexus/plexus-component-annotations {:mvn/version "2.1.0", :exclusions #{junit/junit}, :deps/manifest :mvn, :dependents [org.apache.maven/maven-core], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-component-annotations\\2.1.0\\plexus-component-annotations-2.1.0.jar"]}, com.cognitect.aws/endpoints {:mvn/version "1.1.12.321", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\aws\\endpoints\\1.1.12.321\\endpoints-1.1.12.321.jar"]}, com.google.errorprone/error_prone_annotations {:mvn/version "2.11.0", :deps/manifest :mvn, :dependents [com.google.guava/guava], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.google.guava/guava]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\google\\errorprone\\error_prone_annotations\\2.11.0\\error_prone_annotations-2.11.0.jar"]}, org.apache.commons/commons-lang3 {:mvn/version "3.12.0", :deps/manifest :mvn, :dependents [org.apache.maven.resolver/maven-resolver-impl], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-impl]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-lang3\\3.12.0\\commons-lang3-3.12.0.jar"]}, org.clojure/tools.logging {:mvn/version "1.2.4", :deps/manifest :mvn, :dependents [com.cognitect.aws/api], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.logging\\1.2.4\\tools.logging-1.2.4.jar"]}, org.clojure/core.specs.alpha {:mvn/version "0.4.74", :deps/manifest :mvn, :dependents [org.clojure/clojure], :parents #{[org.clojure/clojure]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.specs.alpha\\0.4.74\\core.specs.alpha-0.4.74.jar"]}, javax.annotation/javax.annotation-api {:mvn/version "1.3.2", :deps/manifest :mvn, :dependents [org.apache.maven.resolver/maven-resolver-impl], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-impl]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\javax\\annotation\\javax.annotation-api\\1.3.2\\javax.annotation-api-1.3.2.jar"]}, org.clojure/spec.alpha {:mvn/version "0.5.238", :deps/manifest :mvn, :dependents [org.clojure/clojure], :parents #{[org.clojure/clojure]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\spec.alpha\\0.5.238\\spec.alpha-0.5.238.jar"]}, org.clojure/tools.cli {:mvn/version "1.0.214", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.cli\\1.0.214\\tools.cli-1.0.214.jar"]}, org.eclipse.jetty/jetty-http {:mvn/version "9.4.48.v20220622", :deps/manifest :mvn, :dependents [org.eclipse.jetty/jetty-client com.cognitect/http-client], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api com.cognitect/http-client org.eclipse.jetty/jetty-client] [io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api com.cognitect/http-client]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-http\\9.4.48.v20220622\\jetty-http-9.4.48.v20220622.jar"]}, org.eclipse.jetty/jetty-util {:mvn/version "9.4.48.v20220622", :deps/manifest :mvn, :dependents [org.eclipse.jetty/jetty-io com.cognitect/http-client org.eclipse.jetty/jetty-http], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api com.cognitect/http-client org.eclipse.jetty/jetty-client org.eclipse.jetty/jetty-io] [io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api com.cognitect/http-client] [io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api com.cognitect/http-client org.eclipse.jetty/jetty-http]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-util\\9.4.48.v20220622\\jetty-util-9.4.48.v20220622.jar"]}, org.slf4j/jcl-over-slf4j {:mvn/version "1.7.36", :deps/manifest :mvn, :dependents [org.apache.maven.resolver/maven-resolver-transport-http], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-http]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\jcl-over-slf4j\\1.7.36\\jcl-over-slf4j-1.7.36.jar"]}, org.clojure/tools.analyzer.jvm {:mvn/version "1.2.2", :deps/manifest :mvn, :dependents [org.clojure/core.async], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api org.clojure/core.async]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.analyzer.jvm\\1.2.2\\tools.analyzer.jvm-1.2.2.jar"]}, org.apache.maven.resolver/maven-resolver-transport-http {:mvn/version "1.8.2", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-transport-http\\1.8.2\\maven-resolver-transport-http-1.8.2.jar"]}, org.apache.maven/maven-model-builder {:mvn/version "3.8.6", :deps/manifest :mvn, :dependents [org.apache.maven/maven-core org.apache.maven/maven-resolver-provider], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-resolver-provider]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-model-builder\\3.8.6\\maven-model-builder-3.8.6.jar"]}, org.codehaus.plexus/plexus-cipher {:mvn/version "2.0", :deps/manifest :mvn, :dependents [org.codehaus.plexus/plexus-sec-dispatcher], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-settings-builder org.codehaus.plexus/plexus-sec-dispatcher]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-cipher\\2.0\\plexus-cipher-2.0.jar"]}, org.codehaus.plexus/plexus-utils {:mvn/version "3.4.1", :deps/manifest :mvn, :dependents [org.codehaus.plexus/plexus-sec-dispatcher], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-settings-builder org.codehaus.plexus/plexus-sec-dispatcher]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-utils\\3.4.1\\plexus-utils-3.4.1.jar"]}, org.apache.maven.resolver/maven-resolver-transport-file {:mvn/version "1.8.2", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-transport-file\\1.8.2\\maven-resolver-transport-file-1.8.2.jar"]}, org.eclipse.sisu/org.eclipse.sisu.plexus {:mvn/version "0.3.5", :exclusions #{javax.enterprise/cdi-api}, :deps/manifest :mvn, :dependents [org.apache.maven/maven-plugin-api org.apache.maven/maven-core], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-plugin-api] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\sisu\\org.eclipse.sisu.plexus\\0.3.5\\org.eclipse.sisu.plexus-0.3.5.jar"]}, commons-io/commons-io {:mvn/version "2.11.0", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\commons-io\\commons-io\\2.11.0\\commons-io-2.11.0.jar"]}, org.apache.maven/maven-settings-builder {:mvn/version "3.8.6", :deps/manifest :mvn, :dependents [org.apache.maven/maven-core], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-settings-builder\\3.8.6\\maven-settings-builder-3.8.6.jar"]}, com.google.guava/listenablefuture {:mvn/version "9999.0-empty-to-avoid-conflict-with-guava", :deps/manifest :mvn, :dependents [com.google.guava/guava], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.google.guava/guava]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\listenablefuture\\9999.0-empty-to-avoid-conflict-with-guava\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"]}, org.clojure/tools.namespace {:mvn/version "1.4.4", :deps/manifest :mvn, :dependents [io.github.clojure/tools.build], :parents #{[io.github.clojure/tools.build]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.namespace\\1.4.4\\tools.namespace-1.4.4.jar"]}, org.ow2.asm/asm {:mvn/version "9.2", :deps/manifest :mvn, :dependents [org.clojure/tools.analyzer.jvm], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api org.clojure/core.async org.clojure/tools.analyzer.jvm]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\ow2\\asm\\asm\\9.2\\asm-9.2.jar"]}, org.apache.maven/maven-settings {:mvn/version "3.8.6", :deps/manifest :mvn, :dependents [org.apache.maven/maven-core org.apache.maven/maven-settings-builder], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-settings-builder]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-settings\\3.8.6\\maven-settings-3.8.6.jar"]}, org.apache.httpcomponents/httpcore {:mvn/version "4.4.15", :deps/manifest :mvn, :dependents [org.apache.maven.resolver/maven-resolver-transport-http], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-http]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpcore\\4.4.15\\httpcore-4.4.15.jar"]}, org.codehaus.plexus/plexus-sec-dispatcher {:mvn/version "2.0", :deps/manifest :mvn, :dependents [org.apache.maven/maven-settings-builder], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-settings-builder]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-sec-dispatcher\\2.0\\plexus-sec-dispatcher-2.0.jar"]}, org.apache.maven/maven-core {:mvn/version "3.8.6", :exclusions #{commons-io/commons-io com.google.guava/guava}, :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-core\\3.8.6\\maven-core-3.8.6.jar"]}, org.apache.maven.resolver/maven-resolver-api {:mvn/version "1.8.2", :deps/manifest :mvn, :dependents [org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-connector-basic org.apache.maven.resolver/maven-resolver-transport-http org.apache.maven.resolver/maven-resolver-spi org.apache.maven.resolver/maven-resolver-util org.apache.maven.resolver/maven-resolver-impl org.apache.maven.resolver/maven-resolver-transport-file], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-connector-basic] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-http] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-spi] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-util] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-impl] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-file]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-api\\1.8.2\\maven-resolver-api-1.8.2.jar"]}, com.cognitect/http-client {:mvn/version "1.0.115", :deps/manifest :mvn, :dependents [com.cognitect.aws/api], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\http-client\\1.0.115\\http-client-1.0.115.jar"]}, org.apache.maven/maven-resolver-provider {:mvn/version "3.8.6", :deps/manifest :mvn, :dependents [org.clojure/tools.deps org.apache.maven/maven-core], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-resolver-provider\\3.8.6\\maven-resolver-provider-3.8.6.jar"]}, org.apache.maven.shared/maven-shared-utils {:mvn/version "3.3.4", :deps/manifest :mvn, :dependents [org.apache.maven/maven-core], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\shared\\maven-shared-utils\\3.3.4\\maven-shared-utils-3.3.4.jar"]}, org.clojure/java.classpath {:mvn/version "1.0.0", :deps/manifest :mvn, :dependents [org.clojure/tools.namespace], :parents #{[io.github.clojure/tools.build org.clojure/tools.namespace]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\java.classpath\\1.0.0\\java.classpath-1.0.0.jar"]}, com.google.guava/failureaccess {:mvn/version "1.0.1", :deps/manifest :mvn, :dependents [com.google.guava/guava], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.google.guava/guava]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\failureaccess\\1.0.1\\failureaccess-1.0.1.jar"]}, com.google.guava/guava {:mvn/version "31.1-android", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\guava\\31.1-android\\guava-31.1-android.jar"]}, org.clojure/data.xml {:mvn/version "0.2.0-alpha8", :deps/manifest :mvn, :dependents [org.clojure/tools.deps com.cognitect.aws/api], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps] [io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\data.xml\\0.2.0-alpha8\\data.xml-0.2.0-alpha8.jar"]}, org.apache.maven.resolver/maven-resolver-spi {:mvn/version "1.8.2", :deps/manifest :mvn, :dependents [org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-connector-basic org.apache.maven.resolver/maven-resolver-transport-http org.apache.maven.resolver/maven-resolver-impl org.apache.maven.resolver/maven-resolver-transport-file], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-connector-basic] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-http] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-impl] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-file]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-spi\\1.8.2\\maven-resolver-spi-1.8.2.jar"]}, com.google.j2objc/j2objc-annotations {:mvn/version "1.3", :deps/manifest :mvn, :dependents [com.google.guava/guava], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.google.guava/guava]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\google\\j2objc\\j2objc-annotations\\1.3\\j2objc-annotations-1.3.jar"]}, org.slf4j/slf4j-nop {:mvn/version "1.7.36", :deps/manifest :mvn, :dependents [io.github.clojure/tools.build], :parents #{[io.github.clojure/tools.build]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-nop\\1.7.36\\slf4j-nop-1.7.36.jar"]}, org.codehaus.plexus/plexus-classworlds {:mvn/version "2.6.0", :deps/manifest :mvn, :dependents [org.apache.maven/maven-plugin-api org.apache.maven/maven-core], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-plugin-api] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-classworlds\\2.6.0\\plexus-classworlds-2.6.0.jar"]}, org.clojure/tools.deps {:mvn/version "0.18.1354", :deps/manifest :mvn, :dependents [io.github.clojure/tools.build], :parents #{[io.github.clojure/tools.build]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.deps\\0.18.1354\\tools.deps-0.18.1354.jar"]}, org.codehaus.plexus/plexus-interpolation {:mvn/version "1.26", :deps/manifest :mvn, :dependents [org.apache.maven/maven-core org.apache.maven/maven-model-builder org.apache.maven/maven-settings-builder], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-resolver-provider org.apache.maven/maven-model-builder] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-settings-builder]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\plexus\\plexus-interpolation\\1.26\\plexus-interpolation-1.26.jar"]}, org.apache.httpcomponents/httpclient {:mvn/version "4.5.13", :exclusions #{commons-logging/commons-logging}, :deps/manifest :mvn, :dependents [org.apache.maven.resolver/maven-resolver-transport-http], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-http]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpclient\\4.5.13\\httpclient-4.5.13.jar"]}, org.checkerframework/checker-qual {:mvn/version "3.12.0", :deps/manifest :mvn, :dependents [com.google.guava/guava], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.google.guava/guava]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\checkerframework\\checker-qual\\3.12.0\\checker-qual-3.12.0.jar"]}, com.google.inject/guice$no_aop {:mvn/version "4.2.2", :deps/manifest :mvn, :dependents [org.apache.maven/maven-core], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\google\\inject\\guice\\4.2.2\\guice-4.2.2-no_aop.jar"]}, org.eclipse.jetty/jetty-client {:mvn/version "9.4.48.v20220622", :deps/manifest :mvn, :dependents [com.cognitect/http-client], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api com.cognitect/http-client]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-client\\9.4.48.v20220622\\jetty-client-9.4.48.v20220622.jar"]}, org.eclipse.jetty/jetty-io {:mvn/version "9.4.48.v20220622", :deps/manifest :mvn, :dependents [org.eclipse.jetty/jetty-client org.eclipse.jetty/jetty-http], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api com.cognitect/http-client org.eclipse.jetty/jetty-client] [io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api com.cognitect/http-client org.eclipse.jetty/jetty-http]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-io\\9.4.48.v20220622\\jetty-io-9.4.48.v20220622.jar"]}, org.clojure/tools.reader {:mvn/version "1.3.6", :deps/manifest :mvn, :dependents [org.clojure/tools.namespace org.clojure/tools.analyzer.jvm], :parents #{[io.github.clojure/tools.build org.clojure/tools.namespace] [io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api org.clojure/core.async org.clojure/tools.analyzer.jvm]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.reader\\1.3.6\\tools.reader-1.3.6.jar"]}, org.clojure/tools.gitlibs {:mvn/version "2.5.197", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\tools.gitlibs\\2.5.197\\tools.gitlibs-2.5.197.jar"]}, org.apache.maven.resolver/maven-resolver-connector-basic {:mvn/version "1.8.2", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-connector-basic\\1.8.2\\maven-resolver-connector-basic-1.8.2.jar"]}, com.cognitect.aws/s3 {:mvn/version "822.2.1145.0", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\cognitect\\aws\\s3\\822.2.1145.0\\s3-822.2.1145.0.jar"]}, io.github.clojure/tools.build {:git/tag "v0.9.6", :git/sha "8e78bccc35116f6b6fc0bf0c125dba8b8db8da6b", :git/url "https://github.com/clojure/tools.build.git", :deps/manifest :deps, :deps/root "C:\\Users\\<USER>\\.gitlibs\\libs\\io.github.clojure\\tools.build\\8e78bccc35116f6b6fc0bf0c125dba8b8db8da6b", :parents #{[]}, :paths ["C:\\Users\\<USER>\\.gitlibs\\libs\\io.github.clojure\\tools.build\\8e78bccc35116f6b6fc0bf0c125dba8b8db8da6b\\src\\main\\clojure" "C:\\Users\\<USER>\\.gitlibs\\libs\\io.github.clojure\\tools.build\\8e78bccc35116f6b6fc0bf0c125dba8b8db8da6b\\src\\main\\resources"]}, org.apache.maven.resolver/maven-resolver-impl {:mvn/version "1.8.2", :deps/manifest :mvn, :dependents [org.clojure/tools.deps], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-impl\\1.8.2\\maven-resolver-impl-1.8.2.jar"]}, org.slf4j/slf4j-api {:mvn/version "1.7.36", :deps/manifest :mvn, :dependents [org.slf4j/jcl-over-slf4j org.apache.maven.resolver/maven-resolver-connector-basic org.apache.maven.resolver/maven-resolver-transport-http org.apache.maven/maven-core org.apache.maven.resolver/maven-resolver-named-locks org.apache.maven.resolver/maven-resolver-impl org.apache.maven.resolver/maven-resolver-transport-file org.slf4j/slf4j-nop], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-http org.slf4j/jcl-over-slf4j] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-connector-basic] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-http] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-impl org.apache.maven.resolver/maven-resolver-named-locks] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-impl] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-file] [io.github.clojure/tools.build org.slf4j/slf4j-nop]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-api\\1.7.36\\slf4j-api-1.7.36.jar"]}, org.apache.maven/maven-model {:mvn/version "3.8.6", :deps/manifest :mvn, :dependents [org.apache.maven/maven-plugin-api org.apache.maven/maven-core org.apache.maven/maven-model-builder org.apache.maven/maven-resolver-provider], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-plugin-api] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-resolver-provider org.apache.maven/maven-model-builder] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-resolver-provider]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-model\\3.8.6\\maven-model-3.8.6.jar"]}, org.eclipse.sisu/org.eclipse.sisu.inject {:mvn/version "0.3.5", :deps/manifest :mvn, :dependents [org.apache.maven/maven-core org.apache.maven/maven-model-builder org.eclipse.sisu/org.eclipse.sisu.plexus], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-resolver-provider org.apache.maven/maven-model-builder] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.eclipse.sisu/org.eclipse.sisu.plexus]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\sisu\\org.eclipse.sisu.inject\\0.3.5\\org.eclipse.sisu.inject-0.3.5.jar"]}, org.apache.maven.resolver/maven-resolver-util {:mvn/version "1.8.2", :deps/manifest :mvn, :dependents [org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-connector-basic org.apache.maven.resolver/maven-resolver-transport-http org.apache.maven.resolver/maven-resolver-impl], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-connector-basic] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-transport-http] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-impl]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-util\\1.8.2\\maven-resolver-util-1.8.2.jar"]}, org.apache.maven.resolver/maven-resolver-named-locks {:mvn/version "1.8.2", :deps/manifest :mvn, :dependents [org.apache.maven.resolver/maven-resolver-impl], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven.resolver/maven-resolver-impl]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\resolver\\maven-resolver-named-locks\\1.8.2\\maven-resolver-named-locks-1.8.2.jar"]}, org.clojure/core.memoize {:mvn/version "1.0.253", :deps/manifest :mvn, :dependents [org.clojure/tools.analyzer.jvm], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api org.clojure/core.async org.clojure/tools.analyzer.jvm]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.memoize\\1.0.253\\core.memoize-1.0.253.jar"]}, org.apache.maven/maven-repository-metadata {:mvn/version "3.8.6", :deps/manifest :mvn, :dependents [org.apache.maven/maven-core org.apache.maven/maven-resolver-provider], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-resolver-provider]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-repository-metadata\\3.8.6\\maven-repository-metadata-3.8.6.jar"]}, org.clojure/data.priority-map {:mvn/version "1.1.0", :deps/manifest :mvn, :dependents [org.clojure/core.cache], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api org.clojure/core.async org.clojure/tools.analyzer.jvm org.clojure/core.memoize org.clojure/core.cache]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\data.priority-map\\1.1.0\\data.priority-map-1.1.0.jar"]}, aopalliance/aopalliance {:mvn/version "1.0", :deps/manifest :mvn, :dependents [com.google.inject/guice$no_aop], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core com.google.inject/guice$no_aop]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\aopalliance\\aopalliance\\1.0\\aopalliance-1.0.jar"]}, org.apache.maven/maven-builder-support {:mvn/version "3.8.6", :deps/manifest :mvn, :dependents [org.apache.maven/maven-core org.apache.maven/maven-model-builder org.apache.maven/maven-settings-builder], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-resolver-provider org.apache.maven/maven-model-builder] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-settings-builder]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-builder-support\\3.8.6\\maven-builder-support-3.8.6.jar"]}, com.google.code.findbugs/jsr305 {:mvn/version "3.0.2", :deps/manifest :mvn, :dependents [com.google.guava/guava], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.google.guava/guava]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\com\\google\\code\\findbugs\\jsr305\\3.0.2\\jsr305-3.0.2.jar"]}, org.clojure/core.cache {:mvn/version "1.0.225", :deps/manifest :mvn, :dependents [org.clojure/core.memoize], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api org.clojure/core.async org.clojure/tools.analyzer.jvm org.clojure/core.memoize]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.cache\\1.0.225\\core.cache-1.0.225.jar"]}, org.apache.maven/maven-plugin-api {:mvn/version "3.8.6", :deps/manifest :mvn, :dependents [org.apache.maven/maven-core], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-plugin-api\\3.8.6\\maven-plugin-api-3.8.6.jar"]}, org.clojure/core.async {:mvn/version "1.6.673", :deps/manifest :mvn, :dependents [com.cognitect.aws/api], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps com.cognitect.aws/api]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\clojure\\core.async\\1.6.673\\core.async-1.6.673.jar"]}, org.apache.maven/maven-artifact {:mvn/version "3.8.6", :exclusions #{org.apache.maven.wagon/wagon-provider-api}, :deps/manifest :mvn, :dependents [org.apache.maven/maven-plugin-api org.apache.maven/maven-core org.apache.maven/maven-model-builder], :parents #{[io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core org.apache.maven/maven-plugin-api] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-core] [io.github.clojure/tools.build org.clojure/tools.deps org.apache.maven/maven-resolver-provider org.apache.maven/maven-model-builder]}, :paths ["C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\maven\\maven-artifact\\3.8.6\\maven-artifact-3.8.6.jar"]}}, :aliases {:deps {:replace-paths [], :replace-deps {org.clojure/tools.deps.cli {:mvn/version "0.11.93"}}, :ns-default clojure.tools.deps.cli.api, :ns-aliases {help clojure.tools.deps.cli.help}}, :test {:extra-paths ["test"], :extra-deps {io.github.cognitect-labs/test-runner {:git/tag "v0.5.1", :git/sha "dfb30dd"}}, :main-opts ["-m" "cognitect.test-runner"], :exec-fn cognitect.test-runner.api/test}, :dev {}, :build {:extra-paths ["src-build"], :deps {io.github.clojure/tools.build {:git/tag "v0.9.6", :git/sha "8e78bcc"}}, :ns-default build}}, :deps {org.clojure/clojure {:mvn/version "1.12.1"}, io.github.clojure/tools.build {:git/tag "v0.9.6", :git/sha "8e78bcc"}}}