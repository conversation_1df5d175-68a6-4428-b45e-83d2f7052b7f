## 1. Setup & Requirements

### Prerequisites

- Install Node.js 16.x or higher
  ```bash
  # Check Node.js version
  node --version
  npm --version
  ```
- Install Android Debug Bridge (ADB)
  - Download and install [Android SDK Platform Tools](https://developer.android.com/tools/adb)
  - Ensure ADB is added to your system PATH
  - Verify installation: `adb version`
- Install clipboard tools (for screenshot functionality):
  - **macOS**: osascript (built-in)
  - **Windows**: PowerShell (built-in)
  - **Linux**: Install xclip
    ```bash
    sudo apt-get install xclip
    ```
- Enable USB debugging on your Android device

## 2. Configure Environment Variables

No additional configuration required.

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.
