## Changed in 0.1.233

* Upgraded Jetty to 9.4.44.v20210927
* Upgraded logback-classic to 1.2.8

## Changed in 0.1.227

* Upgraded Clojure to 1.10.3.
* Upgraded Jetty to 9.4.41.v20210516.
* Upgraded tools.cli to 1.0.206.

## Changed in 0.1.225

* Fixed bug that caused Console to fail to start on newer versions of Datomic.

## Changed in 0.1.223

* Updated jetty dependencies from 9.4.24.v20191120 to
  9.4.27.v20200227.

## Changed in 0.1.222

* Updated jetty dependencies from 9.4.15.v20190215 to
  9.4.24.v20191120.

## Changed in 0.1.214

* Upgrade Clojure dependency to 1.8.0
* Upgrade Vaadin dependency to 7.1.10

## Changed in 0.1.208

* Upgrade Clojure dependency to 1.7.0

## Changed in 0.1.206

* Internal build improvements

## Changed in 0.1.201

* Don't print URLs when `datomic.printConnectionInfo` is `false`.

## Changed in 0.1.199

* Upgrade Clojure dependency to 1.6.0

## Changed in 0.1.172

* Internal build improvements

## Changed in 0.1.170

* UI tweaks

## Changed in 0.1.164

* Provide a way to deselect attributes on Indexes pane.

* UI enhancements

## Changed in 0.1.162

* Usability enhancements

* Fixed classpath bug on Windows

* Be a little more forgiving with a malformed connection string

* Better Infinispan compatibility

## Changed in 0.1.155

* Fixed bug that caused a NPE to be thrown on a brand new database.

* Fixed bug that caused an EDN Parsing exception when clicking on data in browser pane.

* Fixed bug when that caused an exception when trying to view Index Range on a history db.

## Changed in 0.1.150

Initial public release of Datomic Console
