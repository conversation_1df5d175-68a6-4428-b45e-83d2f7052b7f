################################################################
# Free database configuration
protocol=dev
host=localhost
port=4334
alt-host=localhost

# Available storage configurations: free, sql, ddb
storage=free
license-key=agree-to-free-license

# Free storage configuration
data-dir=data
index-dir=data

# H2 storage configuration
# sql-url=jdbc:h2:mem:datomic;DB_CLOSE_DELAY=-1
# sql-user=
# sql-password=

# PostgreSQL storage configuration
# sql-url=****************************************
# sql-user=datomic
# sql-password=datomic

# Metrics configuration
metrics-callback=

# Memory index threshold
memory-index-threshold=32m

# Memory index max
memory-index-max=512m

# Transaction timeout
tx-timeout=10000

# Validation timeout
validation-timeout=10000
