/**
 * Generate embeddings for text using Ollama API
 * @param texts Array of text strings to embed
 * @param embeddingModel Optional model configuration to use
 * @returns Promise containing array of embeddings
 */
export declare function generateOllamaEmbeddings(texts: string[], embeddingModel?: {
    model: string;
    contextSize: number;
    dimensions: number;
    baseUrl?: string;
}): Promise<number[][]>;
