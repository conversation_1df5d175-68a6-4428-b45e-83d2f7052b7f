## 1. 前置准备

### 先决条件

- 安装 Python 3.12 或更高版本
- 安装 uv 包管理器
  ```bash
  # macOS/Linux
  curl -LsSf https://astral.sh/uv/install.sh | sh
  
  # Windows (PowerShell)
  powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
  ```
- 运行在 Windows 系统上（该工具使用 Windows API 进行内存操作）
- 拥有管理员权限（用于访问其他进程的内存）
- 安装依赖包：
  ```bash
  uv add mcp[cli]>=1.6.0 psutil>=7.0.0 pymem>=1.14.0
  ```

## 2. 配置环境变量

不需要额外配置

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

**注意：** 此工具类似 Cheat Engine，用于游戏内存修改。请确保以管理员权限运行，并仅在合法范围内使用。
