## 1. Setup & Requirements

### Prerequisites

- Install Node.js 16 or higher
  ```bash
  # Check Node.js version
  node --version
  ```
- Install Git
  ```bash
  # Check Git version
  git --version
  ```
- Install and configure Ollama
  ```bash
  # Install Ollama (visit https://ollama.ai/ to download)
  # Pull the recommended code embedding model
  ollama pull unclemusclez/jina-embeddings-v2-base-code
  ```
- Ensure Ollama service is running on default port 11434

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `DATA_DIR`: SQLite database storage directory (default: ~/.codeContextMcp/data)
- `REPO_CACHE_DIR`: Cloned repository cache directory (default: ~/.codeContextMcp/repos)
- `NODE_ENV`: Runtime environment (default: development)

**Optional Configuration:**
- `DB_PATH`: Database file name (default: code_context.db)

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

**Notes:** 
- Required directory structure will be created automatically on first run
- Service will automatically check and start Ollama if not running
- Supports cloning Git repositories and performing semantic code search
- Uses SQLite database to store code chunks and embedding vectors
