{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "outDir": "./dist", "rootDir": ".", "declaration": true, "skipLibCheck": true, "isolatedModules": true, "allowJs": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"*": ["*"]}}, "include": ["./**/*.ts", "./**/*.mts", "./tests/**/*.ts"], "exclude": ["node_modules", "dist", "repos"]}