# MCP 项目操作步骤汇总

## 1. cloudflare-api-mcp

### Bun 方式
1. 安装 Bun 运行时环境
2. 获取 Cloudflare API Key 和邮箱地址
3. 设置环境变量 CLOUDFLARE_API_KEY 和 CLOUDFLARE_API_EMAIL
4. 运行 bun create mcp --clone https://github.com/zueai/cloudflare-api-mcp

## 2. datomic-mcp

### Java 方式
1. 安装 Java 8+ 和 Clojure/Leiningen 构建工具
2. 获取 Datomic 数据库连接 URI
3. 构建项目生成 uberjar 文件
4. 设置环境变量 DATOMIC_URI
5. 运行 java -jar target/theronic-datomic-mcp-0.3.0.jar

## 3. trello-mcp-server

### UVX 方式
1. 安装 Python 3.12+ 和 uv 包管理器
2. 获取 Trello API Key 和 Token
3. 设置环境变量 TRELLO_API_KEY、TRELLO_TOKEN、USE_CLAUDE_APP=true
4. 运行 uv run mcp install main.py

### Python 方式
1. 安装 Python 3.12+
2. 获取 Trello API Key 和 Token
3. 设置环境变量 TRELLO_API_KEY、TRELLO_TOKEN、USE_CLAUDE_APP=false
4. 运行 python main.py

### Docker 方式
1. 安装 Docker 和 Docker Compose
2. 获取 Trello API Key 和 Token
3. 创建 .env 文件配置环境变量
4. 运行 docker-compose up -d

## 4. memmcp

### UV 方式
1. 安装 Python 3.12+ 和 uv 包管理器（Windows 系统）
2. 以管理员权限运行
3. 安装依赖 uv add mcp[cli] psutil pymem
4. 运行 uv run main.py

### Python 方式
1. 安装 Python 3.12+（Windows 系统）
2. 以管理员权限运行
3. 安装依赖 pip install "mcp[cli]" "psutil" "pymem"
4. 运行 python main.py

## 5. ticktick-mcp

### UVX 方式
1. 安装 Python 3.10+ 
2. 注册 TickTick 应用获取 Client ID 和 Client Secret
3. 设置环境变量 TICKTICK_CLIENT_ID、TICKTICK_CLIENT_SECRET、TICKTICK_REDIRECT_URI 等
4. 首次运行完成 OAuth2 认证
5. 运行 uvx --from git+https://github.com/jen6/ticktick-mcp.git ticktick-mcp

## 6. mcp_newrelic

### UV 方式
1. 安装 Python 3.10+ 和 uv 包管理器
2. 获取 New Relic API Key 和账户 ID
3. 设置环境变量 NEW_RELIC_API_KEY 和 NEW_RELIC_ACCOUNT_ID
4. 运行 uv run newrelic_logs_server.py

## 7. android-adb-mcp-server

### NPX 方式
1. 安装 Node.js 16+ 和 npm
2. 安装 Android Debug Bridge (ADB) 并添加到 PATH
3. 启用 Android 设备 USB 调试模式
4. 安装剪贴板工具（Linux 需要 xclip）
5. 运行 npx -y @landicefu/android-adb-mcp-server
