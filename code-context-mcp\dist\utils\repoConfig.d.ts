interface RepoConfig {
    url: string;
    localPath?: string;
    lastAccessed: number;
    type: 'local' | 'remote' | 'cached';
    branch?: string;
}
export declare class RepositoryConfigManager {
    private configDir;
    constructor();
    private getConfigPath;
    private sanitizeLocalPath;
    getRepositoryPath(repoUrl: string, branch?: string): {
        path: string;
        config: RepoConfig;
    };
    private createRemoteConfig;
    private saveConfig;
    isLocalRepository(repoUrl: string): boolean;
    needsCloning(repoUrl: string): boolean;
    getRepoType(repoUrl: string): 'local' | 'remote';
}
export declare const repoConfigManager: RepositoryConfigManager;
export {};
