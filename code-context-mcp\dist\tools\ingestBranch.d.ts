import { z } from "zod";
import { ProgressNotifier } from "../utils/types.js";
export declare const IngestBranchSchema: z.ZodObject<{
    repoUrl: z.ZodString;
    branch: z.ZodOptional<z.ZodString>;
    _meta: z.ZodOptional<z.ZodObject<{
        progressToken: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodNumber]>>;
    }, "strip", z.ZodTypeAny, {
        progressToken?: string | number | undefined;
    }, {
        progressToken?: string | number | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    repoUrl: string;
    branch?: string | undefined;
    _meta?: {
        progressToken?: string | number | undefined;
    } | undefined;
}, {
    repoUrl: string;
    branch?: string | undefined;
    _meta?: {
        progressToken?: string | number | undefined;
    } | undefined;
}>;
export declare function ingestBranch(input: z.infer<typeof IngestBranchSchema>, progressNotifier?: ProgressNotifier): Promise<{
    error: {
        message: string;
    };
    repoLocalPath?: undefined;
    repoId?: undefined;
    branchId?: undefined;
    needsUpdate?: undefined;
    repoName?: undefined;
    actualBranch?: undefined;
    latestCommit?: undefined;
} | {
    repoLocalPath: string;
    repoId: any;
    branchId: any;
    needsUpdate: boolean;
    repoName: string;
    actualBranch: string;
    latestCommit: string;
    error?: undefined;
}>;
