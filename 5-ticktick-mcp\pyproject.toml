[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "ticktick-mcp"
version = "0.1.0"
description = "MCP server for interacting with the TickTick API."
authors = [
  { name="jen6", email="<EMAIL>" },
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "anyio>=4.9.0",
    "mcp>=1.6.0",
    "python-dotenv>=1.1.0",
    "ticktick-py",
    "tzlocal>=5.3.1",
]

[project.scripts]
ticktick-mcp = "main:main"

[project.urls]
"Homepage" = "https://github.com/jen6/ticktick-mcp"

[tool.uv.sources]
ticktick-py = { git = "https://github.com/jen6/ticktick-py.git", rev = "main" }

[dependency-groups]
dev = [
    "pytest>=8.3.5",
]
