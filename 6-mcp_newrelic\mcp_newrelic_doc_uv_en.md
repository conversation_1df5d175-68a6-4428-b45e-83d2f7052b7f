## 1. Setup & Requirements

### Prerequisites

- Install Python 3.10 or higher
- Install uv package manager
  ```bash
  # macOS/Linux
  curl -LsSf https://astral.sh/uv/install.sh | sh
  
  # Windows (PowerShell)
  powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
  ```
- Have a New Relic account
- Obtain New Relic API key and account ID:
  - Login to New Relic console
  - Get API Key (User API Key or Ingest License Key)
  - Record your New Relic account ID

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `NEW_RELIC_API_KEY`: Your New Relic API key
- `NEW_RELIC_ACCOUNT_ID`: Your New Relic account ID

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.
