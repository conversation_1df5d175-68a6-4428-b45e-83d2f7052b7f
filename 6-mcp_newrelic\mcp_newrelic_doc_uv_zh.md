## 1. 前置准备

### 先决条件

- 安装 Python 3.10 或更高版本
- 安装 uv 包管理器
  ```bash
  # macOS/Linux
  curl -LsSf https://astral.sh/uv/install.sh | sh
  
  # Windows (PowerShell)
  powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
  ```
- 拥有 New Relic 账户
- 获取 New Relic API 密钥和账户 ID：
  - 登录 New Relic 控制台
  - 获取 API Key（User API Key 或 Ingest License Key）
  - 记录您的 New Relic 账户 ID

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `NEW_RELIC_API_KEY`: 您的 New Relic API 密钥
- `NEW_RELIC_ACCOUNT_ID`: 您的 New Relic 账户 ID

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。
