## 1. 前置准备

### 先决条件

- 安装 Node.js 16.x 或更高版本
  ```bash
  # 检查 Node.js 版本
  node --version
  npm --version
  ```
- 安装 Android Debug Bridge (ADB)
  - 下载并安装 [Android SDK Platform Tools](https://developer.android.com/tools/adb)
  - 确保 ADB 已添加到系统 PATH 中
  - 验证安装：`adb version`
- 安装剪贴板工具（用于截图功能）：
  - **macOS**: osascript（内置）
  - **Windows**: PowerShell（内置）
  - **Linux**: 安装 xclip
    ```bash
    sudo apt-get install xclip
    ```
- 启用 Android 设备的 USB 调试模式

## 2. 配置环境变量

不需要额外配置

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。
