## 1. Setup & Requirements

### Prerequisites

- Install Docker and Docker Compose
  ```bash
  # Check Docker version
  docker --version
  docker-compose --version
  ```
- Have a Trello account
- Obtain Trello API credentials:
  - Go to [Trello Apps Administration](https://trello.com/power-ups/admin)
  - Create a new integration at [New Power-Up or Integration](https://trello.com/power-ups/admin/new)
  - Fill in your information and select the correct Workspace
  - Click your app's icon and navigate to "API key" from left sidebar
  - Copy your "API key" and click the "Token" link on the right to get your Trello Token

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `TRELLO_API_KEY`: Your Trello API key
- `TRELLO_TOKEN`: Your Trello API token
- `USE_CLAUDE_APP`: Set to "false" (Docker mode uses SSE by default)
- `MCP_SERVER_HOST`: Server host address (default: 0.0.0.0)
- `MCP_SERVER_PORT`: Server port (default: 8000)

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.
