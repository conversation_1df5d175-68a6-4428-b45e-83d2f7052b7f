## 1. 前置准备

### 先决条件

- 安装 Node.js 16 或更高版本
  ```bash
  # 检查 Node.js 版本
  node --version
  ```
- 安装 Git
  ```bash
  # 检查 Git 版本
  git --version
  ```
- 安装并配置 Ollama
  ```bash
  # 安装 Ollama (访问 https://ollama.ai/ 下载)
  # 拉取推荐的代码嵌入模型
  ollama pull unclemusclez/jina-embeddings-v2-base-code
  ```
- 确保 Ollama 服务运行在默认端口 11434
- 安装项目依赖并构建
  ```bash
  # 安装依赖
  npm install

  # 构建项目
  npm run build
  ```

## 2. 配置环境变量



## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

