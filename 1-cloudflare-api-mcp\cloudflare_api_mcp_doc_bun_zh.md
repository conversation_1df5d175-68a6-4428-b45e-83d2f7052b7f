## 1. 前置准备

### 先决条件

- 安装 Bun 运行时环境
  ```bash
  curl -fsSL https://bun.sh/install | bash
  ```
- 拥有 Cloudflare 账户
- 获取 Cloudflare API Key 和 Email：
  - 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
  - 进入 "My Profile" > "API Tokens"
  - 创建 API Token 或使用 Global API Key
  - 记录您的 Cloudflare 账户邮箱地址

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `CLOUDFLARE_API_KEY`: 您的 Cloudflare API 密钥
- `CLOUDFLARE_API_EMAIL`: 您的 Cloudflare 账户邮箱地址

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。
