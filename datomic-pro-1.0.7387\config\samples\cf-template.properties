#################################################################
# AWS instance and group settings
#################################################################

# required
# AWS instance type. See http://aws.amazon.com/ec2/instance-types/ for
# a list of legal instance types.
aws-instance-type=c3.large

# required, see http://docs.amazonwebservices.com/general/latest/gr/rande.html#ddb_region
aws-region=us-east-1

# required
# Enable detailed monitoring of AWS instances. 
aws-instance-monitoring=true

# required
# Set group size >1 to create a standby pool for High Availability.
aws-autoscaling-group-size=1

# required, default = 70% of AWS instance RAM
# Passed to java launcher via -Xmx
java-xmx=

#################################################################
# Java VM options
#
# If you set the java-opts property, it will entirely replace the
# value used by bin/transactor, which you should consult as a
# starting point if you are configuring GC.
#
# Note that the single-quoting is necessary due to the whitespace
# between options.
#################################################################
# java-opts='-XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:+CMSParallelRemarkEnabled -XX:CMSInitiatingOccupancyFraction=75 -XX:+UseCMSInitiatingOccupancyOnly'

#################################################################
# security settings
#
# You must specify at least one of aws-ingress-grops or 
# aws-ingress-cidrs to allows peers to connect!
#################################################################
# required
# The transactor needs to run in a security group that opens the
# transactor port to legal peers. If you specify a security group, 
# `bin/transactor ensure-cf ...` will ensure that security group
# allows ingress on the transactor port.
aws-security-group=datomic

# Comma-delimited list of security groups. Security group syntax:
#    group-name or aws-account-id:group-name
aws-ingress-groups=datomic

# Comma-delimited list of CIDRS.
#aws-ingress-cidrs=0.0.0.0/0

#################################################################
# datomic deployment settings
#################################################################
# required, default = VERSION number of Datomic you deploy from
# Which Datomic version to run.
datomic-version=

# required
# download Datomic from this bucket on startup. You typically will not change this.
datomic-deploy-s3-bucket=deploy-a0dbc565-faf2-4760-9b7e-29a8e45f428e


