2025-08-01 11:09:17.847 INFO  default    org.eclipse.jetty.util.log - Logging initialized @2012ms to org.eclipse.jetty.util.log.Slf4jLog
2025-08-01 11:09:18.153 INFO  default    datomic.slf4j.bridge - SLF4J Bridge installed
2025-08-01 11:09:18.165 INFO  default    datomic.transactor - {"datomic.metricsCallback" clojure.core/identity, :tid 26, "datomic.printConnectionInfo" false, "datomic.prefetchProbes" true, "datomic.memcachedLib" "spy", "datomic.txTimeoutMsec" 10000, "datomic.indexWorkDir" #object[java.io.File 0x12f27fd1 ".\\data\\indexer"], "datomic.indexMetrics" true, "datomic.heartbeatIntervalMsec" 5000, "datomic.memoryIndexThreshold" 33554432, "datomic.s3RetryBaseDelay" 100, "datomic.dynamicIndexParallelism" false, "datomic.s3ClientExecutionTimeout" 5500, "datomic.backupBranchConcurrency" 32, "datomic.indexParallelism" 1, "datomic.localMemcachedConfigTimeoutMsec" 100, "datomic.queryPool" 32, "datomic.versionUnique" "7387", "datomic.objectCacheMax" 134217728, "datomic.allowLogOverlap" false, "datomic.externalSortPool" 1, "datomic.ddbClientExecutionTimeout" 1100, "datomic.readAheadPool" 18, "datomic.version" "1.0.7387", "datomic.ddbSocketTimeout" 900, "datomic.s3RequestTimeout" 5000, "datomic.backupUseSegsetStorage" true, "datomic.efsDeletePool" 128, :pid 41776, :event :config/properties, "datomic.dataDir" #object[java.io.File 0x59607ab ".\\data"], "datomic.s3BackupConcurrency" 25, "datomic.s3ConnectionTimeout" 4500, "datomic.memoryIndexMax" 268435456, "datomic.ddbConnectionTimeout" 900, "datomic.s3SocketTimeout" 4500, "datomic.memcachedGetTimeoutMsec" 20, "datomic.valcachePutsPool" 4, "datomic.fileBackupConcurrency" 5, "datomic.writeConcurrency" 4, "datomic.memcachedRepairFromSpyRatio" 0.1, "datomic.localMemcachedAutoDiscovery" false, "datomic.s3MaxRetries" 9, "datomic.podGcDelayMsec" 60000, "datomic.memcachedExpirationDays" 30, "datomic.defaultPartition" :db.part/user, "datomic.indexIOParallelism" 100, "datomic.readConcurrency" 8, "datomic.efsWritePool" 128, "datomic.indexDirScale" 1, "datomic.memcachedAutoDiscovery" false, "datomic.cloudwatchName" "Transactor", "datomic.peerConnectionTTLMsec" 10000, "datomic.memcachedConfigTimeoutMsec" 100, "datomic.deleteConcurrency" 1, "datomic.exciseIOParallelism" 100, "datomic.ddbRequestTimeout" 1000, "datomic.buildRevision" 7387, "datomic.prefetchConcurrency" 8}
2025-08-01 11:09:18.359 INFO  default    datomic.process-monitor - {:event :metrics/initializing, :metricsCallback clojure.core/identity, :phase :begin, :pid 41776, :tid 26}
2025-08-01 11:09:18.360 INFO  default    datomic.process-monitor - {:event :metrics/initializing, :metricsCallback clojure.core/identity, :msec 0.39, :phase :end, :pid 41776, :tid 26}
2025-08-01 11:09:18.360 INFO  default    datomic.process-monitor - {:metrics/started clojure.core/identity, :pid 41776, :tid 26}
2025-08-01 11:09:18.362 INFO  default    datomic.domain - {:event :cache/create, :cache-bytes 134217728, :pid 41776, :tid 34}
2025-08-01 11:09:18.368 INFO  default    datomic.process-monitor - {:AvailableMB 911.0, :ObjectCacheCount 0, :event :metrics, :pid 41776, :tid 34}
2025-08-01 11:09:18.391 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 0, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017758380, :encrypt-channel true}
2025-08-01 11:09:18.392 INFO  default    datomic.transactor - {:event :transactor/start, :args {:log-dir "log", :protocol :dev, :rest-alias "dev", :memory-index-max "256m", :port 4334, :memory-index-threshold "32m", :data-dir "./data", :object-cache-max "128m", :host "localhost", :version "1.0.7387", :encrypt-channel true}, :pid 41776, :tid 26}
2025-08-01 11:09:18.514 INFO  default    o.a.activemq.artemis.core.server - AMQ221000: live Message Broker is starting with configuration Broker Configuration (clustered=false,journalDirectory=./data/artemis,bindingsDirectory=data/bindings,largeMessagesDirectory=data/largemessages,pagingDirectory=data/paging)
2025-08-01 11:09:18.523 INFO  default    o.a.activemq.artemis.core.server - AMQ221057: Global Max Size is being adjusted to 1/2 of the JVM max size (-Xmx). being defined as 536870912
2025-08-01 11:09:18.538 INFO  default    o.a.activemq.artemis.core.server - AMQ221043: Protocol module found: [artemis-server]. Adding protocol support for: CORE
2025-08-01 11:09:18.612 INFO  default    datomic.log-gc - {:gcName "G1 Young Generation", :gcAction "end of minor GC", :gcCause "G1 Evacuation Pause", :event :gc, :duration 6, :pid 41776, :tid 21}
2025-08-01 11:09:18.645 INFO  default    datomic.log-gc - {:gcName "G1 Young Generation", :gcAction "end of minor GC", :gcCause "Metadata GC Threshold", :event :gc, :duration 6, :pid 41776, :tid 21}
2025-08-01 11:09:18.664 INFO  default    datomic.log-gc - {:gcName "G1 Young Generation", :gcAction "end of minor GC", :gcCause "Metadata GC Threshold", :event :gc, :duration 0, :pid 41776, :tid 21}
2025-08-01 11:09:18.844 INFO  default    o.a.activemq.artemis.core.server - AMQ221020: Started NIO Acceptor at localhost:4334 for protocols [CORE]
2025-08-01 11:09:18.845 INFO  default    o.a.activemq.artemis.core.server - AMQ221007: Server is now live
2025-08-01 11:09:18.845 INFO  default    o.a.activemq.artemis.core.server - AMQ221001: Apache ActiveMQ Artemis Message Broker version 2.31.2 [localhost, nodeID=e9c33c0c-6e84-11f0-a89b-a002a58a6bb4] 
2025-08-01 11:09:18.882 INFO  default    datomic.indexer - {:event :indexer/create, :memidx-max 268435456, :pid 41776, :tid 37}
2025-08-01 11:09:18.934 WARN  default    o.a.activemq.artemis.core.server - AMQ222165: No Dead Letter Address configured for queue admin.request688c2fde-eebf-4028-a653-70ee560dcf76 in AddressSettings
2025-08-01 11:09:18.934 WARN  default    o.a.activemq.artemis.core.server - AMQ222166: No Expiry Address configured for queue admin.request688c2fde-eebf-4028-a653-70ee560dcf76 in AddressSettings
2025-08-01 11:09:18.944 WARN  default    o.a.activemq.artemis.core.server - AMQ222165: No Dead Letter Address configured for queue admin.response688c2fde-da00-45ca-a45f-bf25a37eb484 in AddressSettings
2025-08-01 11:09:18.944 WARN  default    o.a.activemq.artemis.core.server - AMQ222166: No Expiry Address configured for queue admin.response688c2fde-da00-45ca-a45f-bf25a37eb484 in AddressSettings
2025-08-01 11:09:23.384 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 1, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017763382, :encrypt-channel true}
2025-08-01 11:09:28.384 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 2, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017768383, :encrypt-channel true}
2025-08-01 11:09:33.384 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 3, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017773383, :encrypt-channel true}
2025-08-01 11:09:38.386 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 4, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017778385, :encrypt-channel true}
2025-08-01 11:09:43.387 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 5, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017783386, :encrypt-channel true}
2025-08-01 11:09:48.389 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 6, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017788388, :encrypt-channel true}
2025-08-01 11:09:53.391 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 7, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017793390, :encrypt-channel true}
2025-08-01 11:09:58.393 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 8, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017798392, :encrypt-channel true}
2025-08-01 11:10:03.396 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 9, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017803395, :encrypt-channel true}
2025-08-01 11:10:08.397 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 10, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017808395, :encrypt-channel true}
2025-08-01 11:10:11.043 INFO  default    datomic.slf4j.bridge - SLF4J Bridge installed
2025-08-01 11:10:13.397 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 11, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017813396, :encrypt-channel true}
2025-08-01 11:10:18.368 INFO  default    datomic.process-monitor - {:GcPauseMsec {:lo 0, :hi 6, :sum 12, :count 3}, :MetricsReport {:lo 1, :hi 1, :sum 1, :count 1}, :HeartbeatMsec {:lo 5000, :hi 5003, :sum 55016, :count 11}, :AvailableMB 945.0, :ObjectCacheCount 0, :event :metrics, :pid 41776, :tid 35}
2025-08-01 11:10:18.397 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 12, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017818396, :encrypt-channel true}
2025-08-01 11:10:18.957 INFO  default    datomic.transactor - {:event :transactor/remote-ips, :ips #{}, :pid 41776, :tid 103}
2025-08-01 11:10:23.399 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 13, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017823398, :encrypt-channel true}
2025-08-01 11:10:28.401 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 14, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017828400, :encrypt-channel true}
2025-08-01 11:10:33.402 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 15, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017833401, :encrypt-channel true}
2025-08-01 11:10:38.403 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 16, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017838402, :encrypt-channel true}
2025-08-01 11:10:43.404 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 17, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017843403, :encrypt-channel true}
2025-08-01 11:10:48.404 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 18, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017848403, :encrypt-channel true}
2025-08-01 11:10:53.403 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 19, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017853403, :encrypt-channel true}
2025-08-01 11:10:58.405 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 20, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017858404, :encrypt-channel true}
2025-08-01 11:11:03.405 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 21, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017863404, :encrypt-channel true}
2025-08-01 11:11:08.406 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 22, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017868405, :encrypt-channel true}
2025-08-01 11:11:13.405 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 23, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017873405, :encrypt-channel true}
2025-08-01 11:11:18.368 INFO  default    datomic.process-monitor - {:tid 35, :ObjectCacheCount 0, :MemoryIndexMB {:lo 0, :hi 0, :sum 0, :count 1}, :AvailableMB 942.0, :RemotePeers {:lo 0, :hi 0, :sum 0, :count 1}, :HeartbeatMsec {:lo 5000, :hi 5002, :sum 60009, :count 12}, :pid 41776, :event :metrics, :MetricsReport {:lo 1, :hi 1, :sum 1, :count 1}}
2025-08-01 11:11:18.407 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 24, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017878406, :encrypt-channel true}
2025-08-01 11:11:18.956 INFO  default    datomic.transactor - {:event :transactor/remote-ips, :ips #{}, :pid 41776, :tid 103}
2025-08-01 11:11:23.409 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 25, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017883408, :encrypt-channel true}
2025-08-01 11:11:27.161 WARN  default    o.a.activemq.artemis.core.server - AMQ222165: No Dead Letter Address configured for queue admin.request688c305f-88e9-41a1-b13a-3c6e666accfd in AddressSettings
2025-08-01 11:11:27.161 WARN  default    o.a.activemq.artemis.core.server - AMQ222166: No Expiry Address configured for queue admin.request688c305f-88e9-41a1-b13a-3c6e666accfd in AddressSettings
2025-08-01 11:11:27.164 WARN  default    o.a.activemq.artemis.core.server - AMQ222165: No Dead Letter Address configured for queue admin.response688c305f-6cb7-49df-8013-b9e4c0ebf7b0 in AddressSettings
2025-08-01 11:11:27.164 WARN  default    o.a.activemq.artemis.core.server - AMQ222166: No Expiry Address configured for queue admin.response688c305f-6cb7-49df-8013-b9e4c0ebf7b0 in AddressSettings
2025-08-01 11:11:27.196 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-catalog", :phase :begin, :pid 41776, :tid 37}
2025-08-01 11:11:27.197 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-catalog", :msec 0.888, :phase :end, :pid 41776, :tid 37}
2025-08-01 11:11:27.198 INFO  default    datomic.kv-cluster - {:tid 37, :phase :begin, :etag nil, :rev 0, :pod-key "pod-catalog", :pid 41776, :event :kv-cluster/update-pod, :tailid "688c305f-0ca3-4f8f-8b6c-492c55da8731", :bufsize 97}
2025-08-01 11:11:27.201 INFO  default    datomic.kv-cluster - {:tid 37, :phase :end, :etag nil, :rev 0, :pod-key "pod-catalog", :pid 41776, :event :kv-cluster/update-pod, :tailid "688c305f-0ca3-4f8f-8b6c-492c55da8731", :msec 3.53, :bufsize 97}
2025-08-01 11:11:27.202 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-catalog", :phase :begin, :pid 41776, :tid 37}
2025-08-01 11:11:27.203 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-catalog", :msec 1.3, :phase :end, :pid 41776, :tid 37}
2025-08-01 11:11:27.507 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-70a1-441c-802e-7a5136395bd1", :bufsize 240, :msec 4.5, :phase :end, :pid 41776, :tid 123}
2025-08-01 11:11:27.507 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-015f-48e3-bf9e-fdfe5ca9c8c2", :bufsize 89, :msec 4.92, :phase :end, :pid 41776, :tid 120}
2025-08-01 11:11:27.507 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-43f4-4c18-be45-fd22a1cd0264", :bufsize 90, :msec 4.02, :phase :end, :pid 41776, :tid 122}
2025-08-01 11:11:27.507 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-26bf-40af-bbe3-5e5a7b39c9aa", :bufsize 2114, :msec 4.07, :phase :end, :pid 41776, :tid 121}
2025-08-01 11:11:27.507 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-dbf5-45ef-96ee-aaeed739185d", :bufsize 146, :msec 0.347, :phase :end, :pid 41776, :tid 123}
2025-08-01 11:11:27.507 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-3bd5-4588-b222-ad1454369d47", :bufsize 61, :msec 0.305, :phase :end, :pid 41776, :tid 122}
2025-08-01 11:11:27.507 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-37f6-438c-a118-9440486a9996", :bufsize 165, :msec 0.452, :phase :end, :pid 41776, :tid 120}
2025-08-01 11:11:27.507 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-d82a-4822-bcf6-68910cd170ea", :bufsize 118, :msec 0.491, :phase :end, :pid 41776, :tid 121}
2025-08-01 11:11:27.508 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-68f3-45cf-b4bb-78c0a4a7973a", :bufsize 133, :msec 0.238, :phase :end, :pid 41776, :tid 122}
2025-08-01 11:11:27.508 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-bebc-4f64-b4ea-de091b34fa74", :bufsize 71, :msec 0.29, :phase :end, :pid 41776, :tid 123}
2025-08-01 11:11:27.508 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-d6e5-4353-8c15-178747651e21", :bufsize 195, :msec 0.272, :phase :end, :pid 41776, :tid 120}
2025-08-01 11:11:27.508 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-3a05-40c4-a1ad-37f666b2be61", :bufsize 65, :msec 0.223, :phase :end, :pid 41776, :tid 122}
2025-08-01 11:11:27.508 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-5864-4542-9757-8914426501e2", :bufsize 103, :msec 0.429, :phase :end, :pid 41776, :tid 121}
2025-08-01 11:11:27.508 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-73e9-41ee-986a-464e7ee086d3", :bufsize 111, :msec 0.289, :phase :end, :pid 41776, :tid 123}
2025-08-01 11:11:27.508 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-2021-4932-9e10-8ffadd660ad9", :bufsize 665, :msec 0.288, :phase :end, :pid 41776, :tid 120}
2025-08-01 11:11:27.508 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-154b-4afb-b416-0b91f7f2e19a", :bufsize 115, :msec 0.209, :phase :end, :pid 41776, :tid 122}
2025-08-01 11:11:27.509 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-efdd-46b2-91a6-20daac9d2404", :bufsize 65, :msec 0.321, :phase :end, :pid 41776, :tid 123}
2025-08-01 11:11:27.509 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-bbc3-4cf5-b2b6-31a6d04c209f", :bufsize 300, :msec 0.319, :phase :end, :pid 41776, :tid 121}
2025-08-01 11:11:27.509 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-bc88-49dd-9bbe-101793b589e2", :bufsize 194, :msec 0.24, :phase :end, :pid 41776, :tid 122}
2025-08-01 11:11:27.509 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-90fb-4bf2-b7d0-1f540cae43cf", :bufsize 65, :msec 0.319, :phase :end, :pid 41776, :tid 120}
2025-08-01 11:11:27.509 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-dbbf-4385-afb0-c511cecdd471", :bufsize 109, :msec 0.247, :phase :end, :pid 41776, :tid 122}
2025-08-01 11:11:27.509 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-073f-471a-ac8b-48df8fc9ab6d", :bufsize 614, :msec 0.289, :phase :end, :pid 41776, :tid 123}
2025-08-01 11:11:27.509 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-cadd-4776-93ca-b5404c18f3b2", :bufsize 67, :msec 0.282, :phase :end, :pid 41776, :tid 120}
2025-08-01 11:11:27.509 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-1ae0-4dd7-a5ba-bc6a26ac861b", :bufsize 679, :msec 0.345, :phase :end, :pid 41776, :tid 121}
2025-08-01 11:11:27.509 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-f920-479f-b887-922c12e61237", :bufsize 3105, :msec 0.235, :phase :end, :pid 41776, :tid 122}
2025-08-01 11:11:27.509 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-58d4-47f3-acb4-67d11a548a61", :bufsize 111, :msec 0.235, :phase :end, :pid 41776, :tid 123}
2025-08-01 11:11:27.510 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-49fa-478d-b48b-542a7f7ac8c4", :bufsize 90, :msec 0.313, :phase :end, :pid 41776, :tid 120}
2025-08-01 11:11:27.510 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-0fe9-44d0-8887-af5bedd233ab", :bufsize 98, :msec 0.216, :phase :end, :pid 41776, :tid 122}
2025-08-01 11:11:27.510 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-ab81-4116-a46c-0c18edf1b78f", :bufsize 90, :msec 0.323, :phase :end, :pid 41776, :tid 121}
2025-08-01 11:11:27.510 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-b0e8-41ed-b9b1-f90b12c085e6", :bufsize 168, :msec 0.228, :phase :end, :pid 41776, :tid 123}
2025-08-01 11:11:27.513 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-fcbf-4bf8-81df-1d597244dfc6", :bufsize 63, :msec 0.486, :phase :end, :pid 41776, :tid 37}
2025-08-01 11:11:27.514 INFO  default    datomic.kv-cluster - {:event :kv-cluster/create-val, :val-key "688c305f-eed3-4b04-831b-7b0d405c43e0", :bufsize 31, :msec 0.359, :phase :end, :pid 41776, :tid 37}
2025-08-01 11:11:27.516 INFO  default    datomic.kv-cluster - {:tid 37, :phase :begin, :etag nil, :rev 0, :pod-key "pod-log-tail/mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :pid 41776, :event :kv-cluster/update-pod, :tailid "688c305f-c969-4f77-a6a1-1c2d42054150", :bufsize 1}
2025-08-01 11:11:27.516 INFO  default    datomic.kv-cluster - {:tid 37, :phase :end, :etag nil, :rev 0, :pod-key "pod-log-tail/mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :pid 41776, :event :kv-cluster/update-pod, :tailid "688c305f-c969-4f77-a6a1-1c2d42054150", :msec 0.536, :bufsize 1}
2025-08-01 11:11:27.516 INFO  default    datomic.update - {:event :transactor/admin-command, :cmd :create-database, :arg {:db-name "mcp-test-db"}, :result {:created "mcp-test-db", :db-id "mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4"}, :pid 41776, :tid 111}
2025-08-01 11:11:28.411 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 26, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017888408, :encrypt-channel true}
2025-08-01 11:11:33.411 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 27, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017893411, :encrypt-channel true}
2025-08-01 11:11:38.412 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 28, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017898412, :encrypt-channel true}
2025-08-01 11:11:43.413 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 29, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017903413, :encrypt-channel true}
2025-08-01 11:11:48.413 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 30, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017908413, :encrypt-channel true}
2025-08-01 11:11:53.415 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 31, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017913414, :encrypt-channel true}
2025-08-01 11:11:58.414 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 32, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017918414, :encrypt-channel true}
2025-08-01 11:12:03.415 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 33, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017923415, :encrypt-channel true}
2025-08-01 11:12:08.417 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 34, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017928416, :encrypt-channel true}
2025-08-01 11:12:13.417 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 35, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017933417, :encrypt-channel true}
2025-08-01 11:12:18.368 INFO  default    datomic.process-monitor - {:tid 35, :ObjectCacheCount 0, :MemoryIndexMB {:lo 0, :hi 0, :sum 0, :count 1}, :StoragePutMsec {:lo 0, :hi 4, :sum 16, :count 32}, :AvailableMB 876.0, :IndexWriteMsec {:lo 6.51, :hi 8.79, :sum 232.02, :count 30}, :RemotePeers {:lo 0, :hi 0, :sum 0, :count 1}, :IndexWriteBatchCount {:lo 30, :hi 30, :sum 30, :count 1}, :HeartbeatMsec {:lo 5000, :hi 5003, :sum 60012, :count 12}, :PoolSharedStorageQueued {:lo 0, :hi 26, :sum 351, :count 30}, :PoolSharedStorageActive {:lo 1, :hi 4, :sum 113, :count 30}, :pid 41776, :event :metrics, :StoragePutBytes {:lo 31, :hi 3105, :sum 10330, :count 32}, :MetricsReport {:lo 1, :hi 1, :sum 1, :count 1}, :PodUpdateMsec {:lo 0, :hi 3, :sum 3, :count 2}, :DbAddFulltextMsec {:lo 0, :hi 49, :sum 61, :count 15}, :PodGetMsec {:lo 0, :hi 1, :sum 1, :count 2}}
2025-08-01 11:12:18.420 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 36, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017938419, :encrypt-channel true}
2025-08-01 11:12:18.958 INFO  default    datomic.transactor - {:event :transactor/remote-ips, :ips #{"127.0.0.1"}, :pid 41776, :tid 103}
2025-08-01 11:12:23.420 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 37, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017943420, :encrypt-channel true}
2025-08-01 11:12:28.421 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 38, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017948420, :encrypt-channel true}
2025-08-01 11:12:33.422 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 39, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017953422, :encrypt-channel true}
2025-08-01 11:12:38.422 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 40, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017958422, :encrypt-channel true}
2025-08-01 11:12:43.423 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 41, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017963423, :encrypt-channel true}
2025-08-01 11:12:48.424 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 42, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017968423, :encrypt-channel true}
2025-08-01 11:12:53.425 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 43, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017973423, :encrypt-channel true}
2025-08-01 11:12:58.423 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 44, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017978423, :encrypt-channel true}
2025-08-01 11:13:03.423 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 45, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017983423, :encrypt-channel true}
2025-08-01 11:13:08.425 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 46, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017988424, :encrypt-channel true}
2025-08-01 11:13:13.425 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 47, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017993424, :encrypt-channel true}
2025-08-01 11:13:18.369 INFO  default    datomic.process-monitor - {:tid 35, :ObjectCacheCount 0, :MemoryIndexMB {:lo 0, :hi 0, :sum 0, :count 1}, :AvailableMB 874.0, :RemotePeers {:lo 1, :hi 1, :sum 1, :count 1}, :HeartbeatMsec {:lo 5000, :hi 5002, :sum 60007, :count 12}, :pid 41776, :event :metrics, :MetricsReport {:lo 1, :hi 1, :sum 1, :count 1}}
2025-08-01 11:13:18.426 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 48, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754017998426, :encrypt-channel true}
2025-08-01 11:13:18.957 INFO  default    datomic.transactor - {:event :transactor/remote-ips, :ips #{"127.0.0.1"}, :pid 41776, :tid 103}
2025-08-01 11:13:23.426 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 49, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754018003426, :encrypt-channel true}
2025-08-01 11:13:28.427 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 50, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754018008426, :encrypt-channel true}
2025-08-01 11:13:33.427 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 51, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754018013427, :encrypt-channel true}
2025-08-01 11:13:36.066 INFO  default    datomic.process-monitor - {:event :metrics/initializing, :metricsCallback clojure.core/identity, :phase :begin, :pid 50228, :tid 1}
2025-08-01 11:13:36.067 INFO  default    datomic.process-monitor - {:event :metrics/initializing, :metricsCallback clojure.core/identity, :msec 0.512, :phase :end, :pid 50228, :tid 1}
2025-08-01 11:13:36.068 INFO  default    datomic.process-monitor - {:metrics/started clojure.core/identity, :pid 50228, :tid 1}
2025-08-01 11:13:36.070 INFO  default    datomic.domain - {:event :cache/create, :cache-bytes 536870912, :pid 50228, :tid 86}
2025-08-01 11:13:36.072 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-catalog", :phase :begin, :pid 50228, :tid 88}
2025-08-01 11:13:36.074 INFO  default    datomic.process-monitor - {:AvailableMB 970.0, :ObjectCacheCount 0, :event :metrics, :pid 50228, :tid 86}
2025-08-01 11:13:36.077 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-catalog", :msec 4.82, :phase :end, :pid 50228, :tid 88}
2025-08-01 11:13:36.100 INFO  default    datomic.peer - {:event :peer/connect-transactor, :host "localhost", :alt-host nil, :port 4334, :version "1.0.7387", :pid 50228, :tid 1}
2025-08-01 11:13:36.106 WARN  default    o.a.activemq.artemis.core.server - AMQ222165: No Dead Letter Address configured for queue admin.request688c30e0-8f51-45b6-9ceb-c41556d2e2b4 in AddressSettings
2025-08-01 11:13:36.106 WARN  default    o.a.activemq.artemis.core.server - AMQ222166: No Expiry Address configured for queue admin.request688c30e0-8f51-45b6-9ceb-c41556d2e2b4 in AddressSettings
2025-08-01 11:13:36.108 WARN  default    o.a.activemq.artemis.core.server - AMQ222165: No Dead Letter Address configured for queue admin.response688c30e0-e346-4ee0-a29c-74ff8473dc2b in AddressSettings
2025-08-01 11:13:36.108 WARN  default    o.a.activemq.artemis.core.server - AMQ222166: No Expiry Address configured for queue admin.response688c30e0-e346-4ee0-a29c-74ff8473dc2b in AddressSettings
2025-08-01 11:13:36.114 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-catalog", :phase :begin, :pid 41776, :tid 37}
2025-08-01 11:13:36.115 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-catalog", :msec 0.712, :phase :end, :pid 41776, :tid 37}
2025-08-01 11:13:36.118 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-catalog", :phase :begin, :pid 41776, :tid 137}
2025-08-01 11:13:36.118 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-catalog", :msec 0.38, :phase :end, :pid 41776, :tid 137}
2025-08-01 11:13:36.119 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-log-tail/mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :phase :begin, :pid 41776, :tid 137}
2025-08-01 11:13:36.119 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-log-tail/mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :msec 0.328, :phase :end, :pid 41776, :tid 137}
2025-08-01 11:13:36.119 INFO  default    datomic.kv-cluster - {:tid 137, :phase :begin, :etag "688c305f-c969-4f77-a6a1-1c2d42054150", :rev 1, :pod-key "pod-log-tail/mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :pid 41776, :event :kv-cluster/update-pod, :tailid "688c305f-c969-4f77-a6a1-1c2d42054150", :bufsize nil}
2025-08-01 11:13:36.120 INFO  default    datomic.kv-cluster - {:tid 137, :phase :end, :etag "688c305f-c969-4f77-a6a1-1c2d42054150", :rev 1, :pod-key "pod-log-tail/mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :pid 41776, :event :kv-cluster/update-pod, :tailid "688c305f-c969-4f77-a6a1-1c2d42054150", :msec 0.322, :bufsize nil}
2025-08-01 11:13:36.121 INFO  default    datomic.update - {:event :transactor/admin-command, :cmd :start-database, :arg "mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :result {:version "1.0.7387", :type :production, :status :started}, :pid 41776, :tid 128}
2025-08-01 11:13:36.121 INFO  default    datomic.update - {:task :segment-prefetch-processor, :event :update/loop, :phase :begin, :pid 41776, :tid 138}
2025-08-01 11:13:36.126 WARN  default    o.a.activemq.artemis.core.server - AMQ222165: No Dead Letter Address configured for queue mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4.tx-submit688c30e0-d961-4d0c-a892-b2a79560a97b in AddressSettings
2025-08-01 11:13:36.126 WARN  default    o.a.activemq.artemis.core.server - AMQ222166: No Expiry Address configured for queue mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4.tx-submit688c30e0-d961-4d0c-a892-b2a79560a97b in AddressSettings
2025-08-01 11:13:36.128 INFO  default    datomic.update - {:task :reader, :event :update/loop, :phase :begin, :pid 41776, :tid 139}
2025-08-01 11:13:36.135 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-log-tail/mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :phase :begin, :pid 50228, :tid 88}
2025-08-01 11:13:36.136 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-log-tail/mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :msec 1.13, :phase :end, :pid 50228, :tid 88}
2025-08-01 11:13:36.167 INFO  default    datomic.log - {:event :log/catchup, :bytes 0, :tail-t 66, :index-t 66, :msec 7, :pid 41776, :tid 37}
2025-08-01 11:13:36.171 INFO  default    datomic.update - {:task :notifier, :event :update/loop, :phase :begin, :pid 41776, :tid 152}
2025-08-01 11:13:36.171 INFO  default    datomic.update - {:task :fressianer, :event :update/loop, :phase :begin, :pid 41776, :tid 151}
2025-08-01 11:13:36.171 INFO  default    datomic.update - {:task :log-treeifier, :event :update/loop, :phase :begin, :pid 41776, :tid 155}
2025-08-01 11:13:36.175 INFO  default    datomic.update - {:task :processor, :event :update/loop, :phase :begin, :pid 41776, :tid 153}
2025-08-01 11:13:36.177 INFO  default    datomic.update - {:task :block-notifier, :event :update/loop, :phase :begin, :pid 41776, :tid 154}
2025-08-01 11:13:36.292 INFO  default    datomic.log - {:event :log/catchup-fulltext, :phase :begin, :pid 50228, :tid 1}
2025-08-01 11:13:36.294 INFO  default    datomic.log - {:event :log/catchup-fulltext, :msec 1.47, :phase :end, :pid 50228, :tid 1}
2025-08-01 11:13:36.294 INFO  default    datomic.log - {:event :log/catchup, :bytes 0, :tail-t 66, :index-t 66, :msec 5, :pid 50228, :tid 1}
2025-08-01 11:13:36.297 WARN  default    o.a.activemq.artemis.core.server - AMQ222165: No Dead Letter Address configured for queue mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4.tx-result688c30e0-18f2-41dd-92e3-ad3b23bb01b4 in AddressSettings
2025-08-01 11:13:36.297 WARN  default    o.a.activemq.artemis.core.server - AMQ222166: No Expiry Address configured for queue mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4.tx-result688c30e0-18f2-41dd-92e3-ad3b23bb01b4 in AddressSettings
2025-08-01 11:13:36.302 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-log-tail/mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :phase :begin, :pid 50228, :tid 88}
2025-08-01 11:13:36.303 INFO  default    datomic.kv-cluster - {:event :kv-cluster/get-pod, :pod-key "pod-log-tail/mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :msec 0.835, :phase :end, :pid 50228, :tid 88}
2025-08-01 11:13:36.310 INFO  default    datomic.log - {:event :log/catchup-fulltext, :phase :begin, :pid 50228, :tid 1}
2025-08-01 11:13:36.310 INFO  default    datomic.log - {:event :log/catchup-fulltext, :msec 0.0555, :phase :end, :pid 50228, :tid 1}
2025-08-01 11:13:36.310 INFO  default    datomic.log - {:event :log/catchup, :bytes 0, :tail-t 66, :index-t 66, :msec 0, :pid 50228, :tid 1}
2025-08-01 11:13:36.316 INFO  default    datomic.peer - {:tid 1, :db-id "mcp-test-db-36858e12-0697-4cce-89aa-9f8b6a4f41e4", :protocol :dev, :db-name "mcp-test-db", :port 4334, :host "localhost", :pid 50228, :event :peer/cache-connection, :system-root "localhost:4334"}
2025-08-01 11:13:38.438 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 52, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754018018437, :encrypt-channel true}
2025-08-01 11:13:43.439 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 53, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754018023439, :encrypt-channel true}
2025-08-01 11:13:48.440 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 54, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754018028440, :encrypt-channel true}
2025-08-01 11:13:53.442 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 55, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754018033441, :encrypt-channel true}
2025-08-01 11:13:58.442 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 56, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754018038441, :encrypt-channel true}
2025-08-01 11:14:03.442 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 57, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754018043442, :encrypt-channel true}
2025-08-01 11:14:08.442 INFO  default    datomic.lifecycle - {:tid 36, :username "SM8DqE//fQ3cJACaDh3I1mHLR29xqN9NYYfh8MLaQBU=", :port 4334, :rev 58, :host "localhost", :pid 41776, :event :transactor/heartbeat, :version "1.0.7387", :timestamp 1754018048442, :encrypt-channel true}
2025-08-01 11:14:11.430 WARN  default    o.a.activemq.artemis.core.server - AMQ222061: Client connection failed, clearing up resources for session 8369ccf1-6e85-11f0-9259-a002a58a6bb4
2025-08-01 11:14:11.430 WARN  default    o.a.activemq.artemis.core.server - AMQ222107: Cleared up resources for session 8369ccf1-6e85-11f0-9259-a002a58a6bb4
2025-08-01 11:14:11.431 WARN  default    o.a.activemq.artemis.core.server - AMQ222061: Client connection failed, clearing up resources for session 836c3df2-6e85-11f0-9259-a002a58a6bb4
2025-08-01 11:14:11.432 WARN  default    o.a.activemq.artemis.core.server - AMQ222107: Cleared up resources for session 836c3df2-6e85-11f0-9259-a002a58a6bb4
