{"files": {"ignore": ["dist/**/*", "node_modules/**/*", "public/**/*", "**/*.css", ".wrangler/**/*", "drizzle/**/*"]}, "linter": {"enabled": true, "rules": {"recommended": true}, "ignore": ["**/*.md", "**/*.css", ".wrangler/**/*", "node_modules/**/*", "drizzle/**/*"]}, "formatter": {"enabled": true, "indentStyle": "tab", "indentWidth": 4}, "javascript": {"formatter": {"semicolons": "asNeeded", "trailingCommas": "none"}}, "json": {"parser": {"allowComments": true}}}