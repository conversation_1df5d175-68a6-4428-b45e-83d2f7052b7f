## 1. 前置准备

### 先决条件

- 安装 Docker 和 Docker Compose
  ```bash
  # 检查 Docker 版本
  docker --version
  docker-compose --version
  ```
- 拥有 Trello 账户
- 获取 Trello API 凭据：
  - 访问 [Trello Apps Administration](https://trello.com/power-ups/admin)
  - 在 [New Power-Up or Integration](https://trello.com/power-ups/admin/new) 创建新集成
  - 填写信息并选择正确的工作区
  - 点击应用图标，从左侧边栏导航到 "API key"
  - 复制 "API key" 并点击右侧的 "Token" 链接获取 Trello Token

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `TRELLO_API_KEY`: 您的 Trello API 密钥
- `TRELLO_TOKEN`: 您的 Trello API 令牌
- `USE_CLAUDE_APP`: 设置为 "false"（Docker 模式默认使用 SSE）
- `MCP_SERVER_HOST`: 服务器主机地址（默认：0.0.0.0）
- `MCP_SERVER_PORT`: 服务器端口（默认：8000）

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。
