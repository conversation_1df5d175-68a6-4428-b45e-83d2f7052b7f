###################################################################

protocol=dev
host=localhost
port=4334



## OPTIONAL #######################################################
## The dev: and free: protocols typically use two ports
## starting with the selected :port, but you can specify the
## second (h2) port explicitly, e.g. for virtualization environs
## that do not issue contiguous ports.

# h2-port=4335



###################################################################
## Security settings for embedded storage (free and dev).


## == Passwords ==
## Datomic free/dev has an embedded storage engine with default
## passwords. You can supply the 'admin' password explicitly with
## 'storage-admin-password', and rotate that later by moving it to
## 'old-storage-admin-password', supplying a new
## 'storage-admin-password'.
# storage-admin-password=
# old-storage-admin-password=

## Peers access storage via the 'datomic' user. You can set/rotate
## the password for 'datomic' using 'storage-datomic-password' and
## 'old-storage-datomic-password' as per above.
## NOTE: If you set the password for 'datomic' peers must connect
## using the same password in the connect URI.
## See https://docs.datomic.com/on-prem/clojure/index.html#datomic.api/connect.
# storage-datomic-password=
# old-storage-datomic-password=

## == Peer access ==
## You can control network access to storage by peers via
## 'storage-access', options are 'local' (the default) and 'remote'.
## NOTE: To enable remote access, you must explicitly specify
## the admin and datomic passwords above.
# storage-access=local



###################################################################
# See https://docs.datomic.com/on-prem/capacity.html


## Recommended settings for -Xmx4g production usage.
# memory-index-threshold=32m
# memory-index-max=512m
# object-cache-max=1g

## Recommended settings for -Xmx1g usage, e.g. dev laptops.
memory-index-threshold=32m
memory-index-max=256m
object-cache-max=128m



## OPTIONAL #######################################################


## Set to false to disable SSL between the peers and the transactor.
# Default: true
# encrypt-channel=true

## Data directory is used for dev: and free: storage, and
## as a temporary directory for all storages.
# data-dir=data

## Transactor will log here, see bin/logback.xml to configure logging.
# log-dir=log

## Transactor will write process pid here on startup
# pid-file=transactor.pid



## OPTIONAL #######################################################
# See https://docs.datomic.com/on-prem/valcache.html
## Valcache configuration.
## Set these valcache properties to a directory on an SSD to enable valcache

# valcache-path=
# valcache-max-gb=



## OPTIONAL #######################################################
# See https://docs.datomic.com/on-prem/storage.html
## Memcached configuration.

# memcached=host:port,host:port,...
# memcached-username=datomic
# memcached-password=datomic



## OPTIONAL #######################################################
# See https://docs.datomic.com/on-prem/capacity.html


## Soft limit on the number of concurrent writes to storage.
# Default: 4, Miniumum: 2
# write-concurrency=4

## Soft limit on the number of concurrent reads to storage.
# Default: 2 times write-concurrency, Miniumum: 2
# read-concurrency=8

## Parallelism in index jobs.
# Default: 1, Maximum: 8
# index-parallelism=1



## OPTIONAL #######################################################
# See https://docs.datomic.com/on-prem/aws.html
## Optional settings for rotating logs to S3
# (Can be auto-generated by bin/datomic ensure-transactor.)

# aws-s3-log-bucket-id=



## OPTIONAL #######################################################
# See https://docs.datomic.com/on-prem/aws.html
## Optional settings for Cloudwatch metrics.
# (Can be auto-generated by bin/datomic ensure-transactor.)

# aws-cloudwatch-region=

## Pick a unique name to distinguish transactor metrics from different systems.
# aws-cloudwatch-dimension-value=your-system-name



## OPTIONAL #######################################################
# See https://docs.datomic.com/on-prem/ha.html


## The transactor will write a heartbeat into storage on this interval.
## A standby transactor will take over if it sees the heartbeat go 
## unwritten  for 2x this interval. If your transactor load leads to 
## long gc pauses, you can increase this number to prevent the standby 
## transactor from unnecessarily taking over during a long gc pause.
# Default: 5000, Miniumum: 5000
# heartbeat-interval-msec=5000



## OPTIONAL #######################################################


## The transactor will use this partition for new entities that
## do not explicitly specify a partition.
# Default: :db.part/user
# default-partition=:db.part/user


