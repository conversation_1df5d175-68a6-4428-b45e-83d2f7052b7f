## 1. Setup & Requirements

### Prerequisites

- Install Java 8 or higher
  ```bash
  # Check Java version
  java -version
  ```
- Install Clojure and Leiningen build tools
  ```bash
  # macOS
  brew install clojure/tools/clojure leiningen
  
  # Linux
  curl -O https://download.clojure.org/install/linux-install-1.11.1.1413.sh
  chmod +x linux-install-1.11.1.1413.sh
  sudo ./linux-install-1.11.1.1413.sh
  
  # Windows
  # 1. Install Clojure:
  # Visit https://clojure.org/guides/install_clojure and follow the installation instructions
  
  # 2. Install Leiningen:
  # Visit https://leiningen.org/ and download lein.bat
  # Save lein.bat to a directory (e.g., C:\Leiningen)
  # Add the directory to your system PATH environment variable
  # Open a new Command Prompt or PowerShell window
  # Run the following command to complete installation:
  lein self-install
  ```
- Have access to a Datomic database
- Obtain Datomic URI connection string

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `DATOMIC_URI`: Your Datomic database connection URI (e.g., datomic:dev://localhost:4334/your-db)

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.
