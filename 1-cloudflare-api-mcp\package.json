{"name": "cloudflare-api-mcp", "scripts": {"dev": "bun check && wrangler dev", "deploy": "workers-mcp docgen src/index.ts && bun check && wrangler deploy --minify", "check": "bunx biome check --write .", "secrets": "wrangler secret bulk secrets.json"}, "dependencies": {"cloudflare": "^4.1.0", "workers-mcp": "^0.0.13"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@cloudflare/workers-types": "^4.20250109.0", "wrangler": "^3.101.0"}}