import { z } from "zod";
import { ProgressNotifier } from "../utils/types.js";
export declare const EmbedFilesSchema: z.ZodObject<{
    repoLocalPath: z.ZodString;
    branchId: z.ZodNumber;
    _meta: z.ZodOptional<z.ZodObject<{
        progressToken: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodNumber]>>;
    }, "strip", z.ZodType<PERSON>ny, {
        progressToken?: string | number | undefined;
    }, {
        progressToken?: string | number | undefined;
    }>>;
}, "strip", z.ZodType<PERSON>ny, {
    repoLocalPath: string;
    branchId: number;
    _meta?: {
        progressToken?: string | number | undefined;
    } | undefined;
}, {
    repoLocalPath: string;
    branchId: number;
    _meta?: {
        progressToken?: string | number | undefined;
    } | undefined;
}>;
export declare function embedFiles(input: z.infer<typeof EmbedFilesSchema>, progressNotifier?: ProgressNotifier): Promise<{
    error: {
        message: string;
    };
    success?: undefined;
    chunksProcessed?: undefined;
} | {
    success: boolean;
    chunksProcessed: number;
    error?: undefined;
}>;
