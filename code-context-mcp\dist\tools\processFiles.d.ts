import { z } from "zod";
import { ProgressNotifier } from "../utils/types.js";
interface RepositoryFile {
    path: string;
    name: string;
    sha: string;
}
interface RepositoryFilesResult {
    files: RepositoryFile[];
    commitSha: string;
}
export declare const ProcessFilesSchema: z.ZodObject<{
    repoLocalPath: z.ZodString;
    repoId: z.ZodNumber;
    branchId: z.ZodNumber;
    actualBranch: z.ZodString;
    needsUpdate: z.ZodBoolean;
    _meta: z.ZodOptional<z.ZodObject<{
        progressToken: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodNumber]>>;
    }, "strip", z.ZodType<PERSON>ny, {
        progressToken?: string | number | undefined;
    }, {
        progressToken?: string | number | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    repoLocalPath: string;
    repoId: number;
    branchId: number;
    actualBranch: string;
    needsUpdate: boolean;
    _meta?: {
        progressToken?: string | number | undefined;
    } | undefined;
}, {
    repoLocalPath: string;
    repoId: number;
    branchId: number;
    actualBranch: string;
    needsUpdate: boolean;
    _meta?: {
        progressToken?: string | number | undefined;
    } | undefined;
}>;
/**
 * Get the files in a repository branch
 * @param repoPath Path to the repository
 * @param branchName Name of the branch
 * @returns List of files with their metadata
 */
export declare const getRepositoryFiles: (repoPath: string, branchName: string) => Promise<RepositoryFilesResult>;
/**
 * Process file content and split into chunks
 * @param branchName Branch name
 * @param repoPath Repository path
 */
export declare const processFileContents: (branchName: string, repoPath: string) => Promise<void>;
export declare function processFiles(input: z.infer<typeof ProcessFilesSchema>, progressNotifier?: ProgressNotifier): Promise<{
    error: {
        message: string;
    };
    needsUpdate?: undefined;
    filesToProcess?: undefined;
    repoLocalPath?: undefined;
} | {
    needsUpdate: boolean;
    filesToProcess: never[];
    error?: undefined;
    repoLocalPath?: undefined;
} | {
    needsUpdate: boolean;
    filesToProcess: any;
    repoLocalPath: string;
    error?: undefined;
}>;
export {};
