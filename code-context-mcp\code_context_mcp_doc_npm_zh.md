## 1. 前置准备

### 先决条件

- 安装 Node.js 16 或更高版本
  ```bash
  # 检查 Node.js 版本
  node --version
  npm --version
  ```
- 安装 Git 版本控制系统
  ```bash
  # 检查 Git 版本
  git --version
  ```
- 安装并配置 Ollama AI 模型服务
  ```bash
  # 1. 访问 https://ollama.ai/ 下载并安装 Ollama
  # 2. 启动 Ollama 服务
  ollama serve
  
  # 3. 拉取推荐的代码嵌入模型（必需）
  ollama pull unclemusclez/jina-embeddings-v2-base-code
  ```
- 确保系统有足够的磁盘空间用于代码仓库缓存和数据库存储

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**必需配置：**
- `DATA_DIR`: SQLite 数据库存储目录（如：/home/<USER>/.codeContextMcp/data）
- `REPO_CACHE_DIR`: Git 仓库克隆缓存目录（如：/home/<USER>/.codeContextMcp/repos）

**可选配置：**
- `NODE_ENV`: 运行环境模式（默认：development）
- `DB_PATH`: 数据库文件名（默认：code_context.db）

**Ollama 配置（自动检测）：**
- Ollama 服务地址：http://127.0.0.1:11434
- 嵌入模型：unclemusclez/jina-embeddings-v2-base-code
- 上下文大小：8192 tokens
- 向量维度：768

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

**功能特性：**
- 🔍 语义代码搜索：基于 AI 嵌入向量的智能代码检索
- 📦 Git 仓库管理：自动克隆和缓存代码仓库
- 🗃️ 本地数据存储：使用 SQLite 数据库高效存储代码块
- 🎯 智能代码分块：自动将代码分割为语义相关的块
- 🔧 灵活过滤：支持文件模式、关键词和排除规则

**注意事项：**
- 首次运行时会自动创建必要的目录结构
- 服务启动时会自动检查 Ollama 服务状态
- 大型代码仓库的初次处理可能需要较长时间
- 建议在 SSD 硬盘上运行以获得更好的性能
