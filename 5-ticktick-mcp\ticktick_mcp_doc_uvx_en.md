## 1. Setup & Requirements

### Prerequisites

- Install Python 3.10 or higher
- Have a TickTick account
- Register a TickTick application to obtain API credentials:
  - Go to [TickTick OpenAPI Documentation](https://developer.ticktick.com/docs#/openapi) and log in
  - Click "Manage Apps" in the top right corner
  - Register a new app by clicking the "+App Name" button
  - Once created, edit the app details and note down the generated `Client ID` and `Client Secret`
  - Set the `OAuth Redirect URL` (e.g., http://localhost:8080/redirect)

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `TICKTICK_CLIENT_ID`: Your TickTick client ID
- `TICKTICK_CLIENT_SECRET`: Your TickTick client secret
- `TICKTICK_REDIRECT_URI`: OAuth redirect URI (must match exactly with app settings)
- `TICKTICK_USERNAME`: Your TickTick login email
- `TICKTICK_PASSWORD`: Your TickTick login password

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

**Note:** First run requires completing OAuth2 authentication flow. The system will automatically open a browser or provide an authentication link.
