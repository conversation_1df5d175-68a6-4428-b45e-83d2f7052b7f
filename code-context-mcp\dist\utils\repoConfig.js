import { existsSync, writeFileSync, readFileSync, mkdirSync } from 'fs';
import { join, basename } from 'path';
import { createHash } from 'crypto';
import config from '../config.js';
export class RepositoryConfigManager {
    constructor() {
        this.configDir = config.REPO_CONFIG_DIR;
        if (!existsSync(this.configDir)) {
            mkdirSync(this.configDir, { recursive: true });
        }
    }
    getConfigPath(repoUrl) {
        const hash = createHash('md5').update(repoUrl).digest('hex');
        return join(this.configDir, `${hash}.json`);
    }
    sanitizeLocalPath(repoUrl) {
        if (repoUrl.startsWith('file://')) {
            const localPath = repoUrl.replace('file://', '');
            return existsSync(localPath) ? localPath : null;
        }
        return null;
    }
    getRepositoryPath(repoUrl, branch) {
        const localPath = this.sanitizeLocalPath(repoUrl);
        if (localPath) {
            const repoConfig = {
                url: repoUrl,
                localPath,
                lastAccessed: Date.now(),
                type: 'local',
                branch
            };
            this.saveConfig(repoUrl, repoConfig);
            return { path: localPath, config: repoConfig };
        }
        const configPath = this.getConfigPath(repoUrl);
        let repoConfig;
        if (existsSync(configPath)) {
            try {
                repoConfig = JSON.parse(readFileSync(configPath, 'utf8'));
                repoConfig.lastAccessed = Date.now();
            }
            catch {
                repoConfig = this.createRemoteConfig(repoUrl, branch);
            }
        }
        else {
            repoConfig = this.createRemoteConfig(repoUrl, branch);
        }
        this.saveConfig(repoUrl, repoConfig);
        return { path: repoConfig.localPath || '', config: repoConfig };
    }
    createRemoteConfig(repoUrl, branch) {
        const repoName = basename(repoUrl.replace('.git', ''));
        const cacheDir = join(this.configDir, 'cache');
        if (!existsSync(cacheDir)) {
            mkdirSync(cacheDir, { recursive: true });
        }
        return {
            url: repoUrl,
            localPath: join(cacheDir, repoName),
            lastAccessed: Date.now(),
            type: 'remote',
            branch
        };
    }
    saveConfig(repoUrl, config) {
        const configPath = this.getConfigPath(repoUrl);
        writeFileSync(configPath, JSON.stringify(config, null, 2));
    }
    isLocalRepository(repoUrl) {
        return repoUrl.startsWith('file://');
    }
    needsCloning(repoUrl) {
        if (this.isLocalRepository(repoUrl)) {
            return false;
        }
        const { config } = this.getRepositoryPath(repoUrl);
        return !config.localPath || !existsSync(config.localPath);
    }
    getRepoType(repoUrl) {
        return this.isLocalRepository(repoUrl) ? 'local' : 'remote';
    }
}
export const repoConfigManager = new RepositoryConfigManager();
