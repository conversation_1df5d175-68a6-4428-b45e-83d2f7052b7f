{"name": "@modelcontextprotocol/server-code-context", "version": "0.1.0", "description": "MCP server for code context from local git repositories", "license": "MIT", "type": "module", "bin": {"mcp-server-code-context": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch", "start": "node dist/index.js", "start:mcp": "node dist/start.js", "inspect": "npm run build && npx @modelcontextprotocol/inspector node dist/index.js"}, "dependencies": {"@langchain/textsplitters": "^0.1.0", "@modelcontextprotocol/sdk": "1.0.1", "axios": "^1.8.4", "better-sqlite3": "^11.9.1", "express": "^4.21.1", "simple-git": "^3.20.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.23.5"}, "devDependencies": {"@types/better-sqlite3": "^7.6.4", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/minimatch": "^5.1.2", "@types/node": "^20.10.0", "jest": "^29.7.0", "jest-environment-node-single-context": "^29.4.0", "shx": "^0.3.4", "ts-jest": "^29.3.0", "tsx": "^4.19.3", "typescript": "^5.6.2"}}