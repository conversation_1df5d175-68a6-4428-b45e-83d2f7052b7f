<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) Nu North America, Inc.
   All rights reserved.
-->
<project  xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd" >
 <modelVersion>4.0.0</modelVersion>
 <groupId>com.datomic</groupId>
 <artifactId>peer</artifactId>
 <name>peer</name>
 <version>1.0.7387</version>
 <organization>
  <name>Nu North America, Inc.</name>
  <url>https://datomic.com</url>
 </organization>
 <licenses>
  <license>
   <name>The Apache Software License, Version 2.0</name>
   <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
   <distribution>repo</distribution>
  </license>
 </licenses>
 <properties>
  <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
 </properties>
 <dependencyManagement></dependencyManagement>
 <dependencies>
  <dependency>
   <groupId>org.clojure</groupId>
   <artifactId>clojure</artifactId>
   <version>1.11.4</version>
  </dependency>
  <dependency>
   <groupId>org.clojure</groupId>
   <artifactId>core.async</artifactId>
   <version>1.8.741</version>
  </dependency>
  <dependency>
   <groupId>org.clojure</groupId>
   <artifactId>tools.cli</artifactId>
   <version>1.0.219</version>
   <exclusions>
    <exclusion>
     <groupId>org.clojure</groupId>
     <artifactId>clojure</artifactId>
    </exclusion>
   </exclusions>
  </dependency>
  <dependency>
   <groupId>org.fressian</groupId>
   <artifactId>fressian</artifactId>
   <version>0.6.8</version>
  </dependency>
  <dependency>
   <groupId>commons-codec</groupId>
   <artifactId>commons-codec</artifactId>
   <version>1.15</version>
  </dependency>
  <dependency>
   <groupId>commons-io</groupId>
   <artifactId>commons-io</artifactId>
   <version>2.15.1</version>
  </dependency>
  <dependency>
   <groupId>com.cognitect</groupId>
   <artifactId>anomalies</artifactId>
   <version>0.1.12</version>
  </dependency>
  <dependency>
   <groupId>com.datomic</groupId>
   <artifactId>java-io</artifactId>
   <version>0.1.29</version>
  </dependency>
  <dependency>
   <groupId>org.slf4j</groupId>
   <artifactId>jul-to-slf4j</artifactId>
   <version>1.7.36</version>
  </dependency>
  <dependency>
   <groupId>org.slf4j</groupId>
   <artifactId>slf4j-api</artifactId>
   <version>1.7.36</version>
  </dependency>
  <dependency>
   <groupId>org.slf4j</groupId>
   <artifactId>log4j-over-slf4j</artifactId>
   <version>1.7.36</version>
   <scope>runtime</scope>
  </dependency>
  <dependency>
   <groupId>org.slf4j</groupId>
   <artifactId>jcl-over-slf4j</artifactId>
   <version>1.7.36</version>
  </dependency>
  <dependency>
   <groupId>org.apache.activemq</groupId>
   <artifactId>artemis-core-client</artifactId>
   <version>2.31.2</version>
   <exclusions>
    <exclusion>
     <groupId>org.jgroups</groupId>
     <artifactId>jgroups</artifactId>
    </exclusion>
    <exclusion>
     <groupId>commons-logging</groupId>
     <artifactId>commons-logging</artifactId>
    </exclusion>
   </exclusions>
  </dependency>
  <dependency>
   <groupId>com.cognitect</groupId>
   <artifactId>caster</artifactId>
   <version>1.0.45</version>
  </dependency>
  <dependency>
   <groupId>com.datomic</groupId>
   <artifactId>query-support</artifactId>
   <version>0.8.31</version>
  </dependency>
  <dependency>
   <groupId>com.datomic</groupId>
   <artifactId>query-stats</artifactId>
   <version>1.0.13</version>
  </dependency>
  <dependency>
   <groupId>com.datomic</groupId>
   <artifactId>io-stats</artifactId>
   <version>1.0.14</version>
  </dependency>
  <dependency>
   <groupId>com.datastax.cassandra</groupId>
   <artifactId>cassandra-driver-core</artifactId>
   <version>3.7.1</version>
   <scope>provided</scope>
   <exclusions>
    <exclusion>
     <groupId>io.netty</groupId>
     <artifactId>*</artifactId>
    </exclusion>
   </exclusions>
  </dependency>
  <dependency>
   <groupId>com.datastax.oss</groupId>
   <artifactId>java-driver-core-shaded</artifactId>
   <version>4.17.0</version>
   <scope>provided</scope>
  </dependency>
  <dependency>
   <groupId>software.aws.mcs</groupId>
   <artifactId>aws-sigv4-auth-cassandra-java-driver-plugin</artifactId>
   <version>4.0.9</version>
   <scope>provided</scope>
   <exclusions>
    <exclusion>
     <groupId>com.datastax.oss</groupId>
     <artifactId>java-driver-core-shaded</artifactId>
    </exclusion>
    <exclusion>
     <groupId>com.datastax.oss</groupId>
     <artifactId>java-driver-core</artifactId>
    </exclusion>
   </exclusions>
  </dependency>
  <dependency>
   <groupId>org.infinispan</groupId>
   <artifactId>infinispan-client-hotrod</artifactId>
   <version>5.1.2.FINAL</version>
   <scope>provided</scope>
   <exclusions>
    <exclusion>
     <groupId>org.rhq.helpers</groupId>
     <artifactId>rhq-pluginAnnotations</artifactId>
    </exclusion>
   </exclusions>
  </dependency>
  <dependency>
   <groupId>com.h2database</groupId>
   <artifactId>h2</artifactId>
   <version>2.1.214</version>
  </dependency>
  <dependency>
   <groupId>com.datomic</groupId>
   <artifactId>datomic-lucene-core</artifactId>
   <version>3.3.0</version>
  </dependency>
  <dependency>
   <groupId>com.github.ben-manes.caffeine</groupId>
   <artifactId>caffeine</artifactId>
   <version>3.1.5</version>
  </dependency>
  <dependency>
   <groupId>com.datomic</groupId>
   <artifactId>memcache-asg-java-client</artifactId>
   <version>1.1.0.36</version>
  </dependency>
  <dependency>
   <groupId>com.amazonaws</groupId>
   <artifactId>aws-java-sdk-cloudwatch</artifactId>
   <version>1.12.564</version>
   <scope>provided</scope>
   <exclusions>
    <exclusion>
     <groupId>org.apache.httpcomponents</groupId>
     <artifactId>httpclient</artifactId>
    </exclusion>
    <exclusion>
     <groupId>commons-logging</groupId>
     <artifactId>commons-logging</artifactId>
    </exclusion>
   </exclusions>
  </dependency>
  <dependency>
   <groupId>com.amazonaws</groupId>
   <artifactId>aws-java-sdk-s3</artifactId>
   <version>1.12.564</version>
   <scope>provided</scope>
   <exclusions>
    <exclusion>
     <groupId>org.apache.httpcomponents</groupId>
     <artifactId>httpclient</artifactId>
    </exclusion>
    <exclusion>
     <groupId>commons-logging</groupId>
     <artifactId>commons-logging</artifactId>
    </exclusion>
   </exclusions>
  </dependency>
  <dependency>
   <groupId>com.amazonaws</groupId>
   <artifactId>aws-java-sdk-dynamodb</artifactId>
   <version>1.12.564</version>
   <scope>provided</scope>
   <exclusions>
    <exclusion>
     <groupId>org.apache.httpcomponents</groupId>
     <artifactId>httpclient</artifactId>
    </exclusion>
    <exclusion>
     <groupId>commons-logging</groupId>
     <artifactId>commons-logging</artifactId>
    </exclusion>
   </exclusions>
  </dependency>
  <dependency>
   <groupId>org.apache.httpcomponents</groupId>
   <artifactId>httpclient</artifactId>
   <version>4.5.13</version>
   <exclusions>
    <exclusion>
     <groupId>commons-logging</groupId>
     <artifactId>commons-logging</artifactId>
    </exclusion>
   </exclusions>
  </dependency>
  <dependency>
   <groupId>org.apache.tomcat</groupId>
   <artifactId>tomcat-jdbc</artifactId>
   <version>7.0.109</version>
   <exclusions>
    <exclusion>
     <groupId>commons-logging</groupId>
     <artifactId>commons-logging</artifactId>
    </exclusion>
   </exclusions>
  </dependency>
  <dependency>
   <groupId>org.postgresql</groupId>
   <artifactId>postgresql</artifactId>
   <version>42.5.1</version>
   <scope>provided</scope>
  </dependency>
  <dependency>
   <groupId>org.codehaus.janino</groupId>
   <artifactId>commons-compiler-jdk</artifactId>
   <version>3.0.12</version>
  </dependency>
 </dependencies>
 <build>
  <sourceDirectory>src/java</sourceDirectory>
  <resources>
   <resource>
    <directory>src/resources</directory>
   </resource>
  </resources>
  <plugins>
   <plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-enforcer-plugin</artifactId>
    <version>1.0-alpha-3</version>
    <executions>
     <execution>
      <id>ban-other-loggers</id>
      <goals>
       <goal>enforce</goal>
      </goals>
      <configuration>
       <rules>
        <bannedDependencies>
         <searchTransitive>true</searchTransitive>
         <excludes>
          <exclude>log4j:log4j</exclude>
          <exclude>commons-logging</exclude>
          <exclude>org.slf4j:slf4j-log4j12</exclude>
         </excludes>
         <message>Other loggers banned for org.slf4j, netty subcomponents banned for netty-all</message>
        </bannedDependencies>
       </rules>
      </configuration>
     </execution>
    </executions>
   </plugin>
   <plugin>
    <artifactId>maven-compiler-plugin</artifactId>
    <version>3.11.0</version>
    <configuration>
     <release>11</release>
    </configuration>
   </plugin>
   <plugin>
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>build-helper-maven-plugin</artifactId>
    <version>1.5</version>
    <executions>
     <execution>
      <id>add-source-dirs</id>
      <phase>generate-sources</phase>
      <goals>
       <goal>add-source</goal>
      </goals>
     </execution>
    </executions>
    <configuration>
     <sources>
      <source>src/java</source>
      <source>target/generated-sources/keywords</source>
     </sources>
    </configuration>
   </plugin>
   <plugin>
    <groupId>com.theoryinpractise</groupId>
    <artifactId>clojure-maven-plugin</artifactId>
    <version>1.3.6</version>
    <configuration>
     <vmargs>-server -Xmx1G -Xms1G -Dclojure.compiler.elide-meta='[:doc :file :line]'</vmargs>
     <sourceDirectories>
      <sourceDirectory>src/clj</sourceDirectory>
     </sourceDirectories>
     <copiedNamespaces>
      <namespace>!.*</namespace>
     </copiedNamespaces>
     <warnOnReflection>true</warnOnReflection>
    </configuration>
    <executions>
     <execution>
      <id>compile-clojure</id>
      <phase>compile</phase>
      <goals>
       <goal>compile</goal>
      </goals>
     </execution>
     <execution>
      <id>test-clojure</id>
      <phase>test</phase>
      <goals>
       <goal>test</goal>
      </goals>
     </execution>
    </executions>
   </plugin>
   <plugin>
    <artifactId>maven-assembly-plugin</artifactId>
    <version>2.2</version>
    <configuration>
     <descriptorId>jar-with-dependencies</descriptorId>
     <descriptors>
      <descriptor>src/assembly/all.xml</descriptor>
     </descriptors>
    </configuration>
   </plugin>
  </plugins>
  <extensions>
   <extension>
    <groupId>org.springframework.build</groupId>
    <artifactId>aws-maven</artifactId>
    <version>5.0.0.RELEASE</version>
   </extension>
  </extensions>
 </build>
 <repositories>
  <repository>
   <id>project</id>
   <url>https://d3cbhizx7bu3at.cloudfront.net</url>
   <releases>
    <enabled>true</enabled>
   </releases>
   <snapshots>
    <enabled>false</enabled>
   </snapshots>
  </repository>
  <repository>
   <id>Sonatype</id>
   <name>Sonatype Release</name>
   <url>https://oss.sonatype.org/content/repositories/releases</url>
   <releases>
    <enabled>true</enabled>
   </releases>
   <snapshots>
    <enabled>false</enabled>
   </snapshots>
  </repository>
  <repository>
   <id>Central</id>
   <name>Maven Central</name>
   <url>https://repo.maven.apache.org/maven2</url>
   <releases>
    <enabled>true</enabled>
   </releases>
   <snapshots>
    <enabled>false</enabled>
   </snapshots>
  </repository>
  <repository>
   <id>jboss</id>
   <url>https://repository.jboss.org/nexus/content/groups/public</url>
   <releases>
    <enabled>true</enabled>
   </releases>
   <snapshots>
    <enabled>false</enabled>
   </snapshots>
  </repository>
  <repository>
   <id>cloud-maven</id>
   <url>s3://cloud-1fc2183a-56c5-4f05-b37d-e78b6a7daffe/maven/releases</url>
   <releases>
    <enabled>true</enabled>
   </releases>
   <snapshots>
    <enabled>false</enabled>
   </snapshots>
  </repository>
 </repositories>
</project>
