## 1. Setup & Requirements

### Prerequisites

- Install Node.js 16 or higher
  ```bash
  # Check Node.js version
  node --version
  npm --version
  ```
- Install Git version control system
  ```bash
  # Check Git version
  git --version
  ```
- Install and configure Ollama AI model service
  ```bash
  # 1. Visit https://ollama.ai/ to download and install Ollama
  # 2. Start Ollama service
  ollama serve
  
  # 3. Pull the recommended code embedding model (required)
  ollama pull unclemusclez/jina-embeddings-v2-base-code
  ```
- Ensure sufficient disk space for repository cache and database storage

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Required Configuration:**
- `DATA_DIR`: SQLite database storage directory (e.g., /home/<USER>/.codeContextMcp/data)
- `REPO_CACHE_DIR`: Git repository clone cache directory (e.g., /home/<USER>/.codeContextMcp/repos)

**Optional Configuration:**
- `NODE_ENV`: Runtime environment mode (default: development)
- `DB_PATH`: Database file name (default: code_context.db)

**Ollama Configuration (Auto-detected):**
- Ollama service URL: http://127.0.0.1:11434
- Embedding model: unclemusclez/jina-embeddings-v2-base-code
- Context size: 8192 tokens
- Vector dimensions: 768

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

**Key Features:**
- 🔍 Semantic Code Search: AI-powered intelligent code retrieval using embedding vectors
- 📦 Git Repository Management: Automatic cloning and caching of code repositories
- 🗃️ Local Data Storage: Efficient code chunk storage using SQLite database
- 🎯 Smart Code Chunking: Automatic segmentation of code into semantically related blocks
- 🔧 Flexible Filtering: Support for file patterns, keywords, and exclusion rules

**Important Notes:**
- Required directory structure will be created automatically on first run
- Service will automatically check Ollama service status on startup
- Initial processing of large repositories may take considerable time
- Recommended to run on SSD storage for better performance
