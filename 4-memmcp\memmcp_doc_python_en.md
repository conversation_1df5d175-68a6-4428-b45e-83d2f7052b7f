## 1. Setup & Requirements

### Prerequisites

- Install Python 3.12 or higher
- Run on Windows system (this tool uses Windows API for memory operations)
- Have administrator privileges (required to access other processes' memory)
- Install dependencies:
  ```bash
  pip install "mcp[cli]>=1.6.0" "psutil>=7.0.0" "pymem>=1.14.0"
  ```

## 2. Configure Environment Variables

No additional configuration required.

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.

**Note:** This tool is similar to Cheat Engine for game memory modification. Please ensure to run with administrator privileges and use only within legal boundaries. This mode will run as SSE server at http://127.0.0.1:8000/sse.
