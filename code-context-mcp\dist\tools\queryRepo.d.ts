import { z } from "zod";
import { ProgressNotifier } from "../utils/types.js";
export declare const QueryRepoSchema: z.ZodObject<{
    repoUrl: z.ZodString;
    branch: z.ZodOptional<z.ZodString>;
    semanticSearch: z.ZodString;
    keywordsSearch: z.<PERSON><z.ZodString, "many">;
    filePatterns: z.<PERSON><z.ZodString, "many">;
    excludePatterns: z.Zod<PERSON>ptional<z.ZodArray<z.ZodString, "many">>;
    limit: z.ZodOptional<z.ZodNumber>;
    _meta: z.ZodOptional<z.ZodObject<{
        progressToken: z.ZodOptional<z.ZodU<PERSON>n<[z.ZodString, z.ZodNumber]>>;
    }, "strip", z.ZodTypeAny, {
        progressToken?: string | number | undefined;
    }, {
        progressToken?: string | number | undefined;
    }>>;
}, "strip", z.<PERSON>od<PERSON><PERSON>Any, {
    repoUrl: string;
    semanticSearch: string;
    keywordsSearch: string[];
    filePatterns: string[];
    branch?: string | undefined;
    _meta?: {
        progressToken?: string | number | undefined;
    } | undefined;
    excludePatterns?: string[] | undefined;
    limit?: number | undefined;
}, {
    repoUrl: string;
    semanticSearch: string;
    keywordsSearch: string[];
    filePatterns: string[];
    branch?: string | undefined;
    _meta?: {
        progressToken?: string | number | undefined;
    } | undefined;
    excludePatterns?: string[] | undefined;
    limit?: number | undefined;
}>;
export declare function queryRepo(input: z.infer<typeof QueryRepoSchema>, progressNotifier?: ProgressNotifier): Promise<{
    error: {
        message: string;
    } | undefined;
    output?: undefined;
} | {
    output: {
        success: boolean;
        repoUrl: string;
        branch: string;
        processingTimeMs: number;
        results: any;
    };
    error?: undefined;
}>;
