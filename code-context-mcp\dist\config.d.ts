export declare const EMBEDDING_MODELS: {
    OLLAMA: {
        model: string;
        contextSize: number;
        dimensions: number;
        baseUrl: string;
    };
};
export declare const codeContextConfig: {
    ENV: string;
    REPO_CONFIG_DIR: string;
    BATCH_SIZE: number;
    DATA_DIR: string;
    DB_PATH: string;
    EMBEDDING_MODEL: {
        model: string;
        contextSize: number;
        dimensions: number;
        baseUrl: string;
    };
};
export default codeContextConfig;
