## 1. Setup & Requirements

### Prerequisites

- Install Python 3.12 or higher
- Install uv package manager
  ```bash
  curl -LsSf https://astral.sh/uv/install.sh | sh
  ```
- Have a Trello account
- Obtain Trello API credentials:
  - Go to [Trello Apps Administration](https://trello.com/power-ups/admin)
  - Create a new integration at [New Power-Up or Integration](https://trello.com/power-ups/admin/new)
  - Fill in your information and select the correct Workspace
  - Click your app's icon and navigate to "API key" from left sidebar
  - Copy your "API key" and click the "Token" link on the right to get your Trello Token

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `TRELLO_API_KEY`: Your Trello API key
- `TRELLO_TOKEN`: Your Trello API token
- `USE_CLAUDE_APP`: Set to "true" (default value)

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.
