## 1. 前置准备

### 先决条件

- 安装 Node.js 16 或更高版本
  ```bash
  # 检查 Node.js 版本
  node --version
  ```
- 安装 Git
  ```bash
  # 检查 Git 版本
  git --version
  ```
- 安装并配置 Ollama
  ```bash
  # 安装 Ollama (访问 https://ollama.ai/ 下载)
  # 拉取推荐的代码嵌入模型
  ollama pull unclemusclez/jina-embeddings-v2-base-code
  ```
- 确保 Ollama 服务运行在默认端口 11434

## 2. 配置环境变量

在 MCP NOW 客户端中设置以下环境变量：

**配置说明：**
- `DATA_DIR`: SQLite 数据库存储目录（默认：~/.codeContextMcp/data）
- `REPO_CACHE_DIR`: 克隆的代码仓库缓存目录（默认：~/.codeContextMcp/repos）
- `NODE_ENV`: 运行环境（默认：development）

**可选配置：**
- `DB_PATH`: 数据库文件名（默认：code_context.db）

## 3. 启动服务

完成以上步骤后，点击下方的 **安装** 按钮连接并启动服务。

**注意：** 
- 首次运行时会自动创建必要的目录结构
- 服务会自动检查并启动 Ollama（如果未运行）
- 支持克隆 Git 仓库并进行语义代码搜索
- 使用 SQLite 数据库存储代码块和嵌入向量
