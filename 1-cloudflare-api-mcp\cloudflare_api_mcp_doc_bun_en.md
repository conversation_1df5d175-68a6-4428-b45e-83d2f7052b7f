## 1. Setup & Requirements

### Prerequisites

- Install Bun runtime environment
  ```bash
  curl -fsSL https://bun.sh/install | bash
  ```
- Have a Cloudflare account
- Obtain Cloudflare API Key and Email:
  - Login to [Cloudflare Dashboard](https://dash.cloudflare.com/)
  - Go to "My Profile" > "API Tokens"
  - Create an API Token or use Global API Key
  - Record your Cloudflare account email address

## 2. Configure Environment Variables

Configure the following environment variables in MCP NOW client:

**Configuration Details:**
- `CLOUDFLARE_API_KEY`: Your Cloudflare API key
- `CLOUDFLARE_API_EMAIL`: Your Cloudflare account email address

## 3. Launch the Server

After completing the above steps, click the **Install** button below to connect and start the service.
