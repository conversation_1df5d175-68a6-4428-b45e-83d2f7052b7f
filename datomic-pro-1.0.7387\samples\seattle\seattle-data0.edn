[
{:district/region :region/e, :db/id #db/id[:db.part/user -1000001], :district/name "East"}
{:db/id #db/id[:db.part/user -1000002], :neighborhood/name "Capitol Hill", :neighborhood/district #db/id[:db.part/user -1000001]}
{:community/category ["15th avenue residents"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000003], :community/name "15th Ave Community", :community/url "http://groups.yahoo.com/group/15thAve_Community/", :community/neighborhood #db/id[:db.part/user -1000002]}
{:district/region :region/sw, :db/id #db/id[:db.part/user -1000004], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000005], :neighborhood/name "Admiral (West Seattle)", :neighborhood/district #db/id[:db.part/user -1000004]}
{:community/category ["neighborhood association"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000006], :community/name "Admiral Neighborhood Association", :community/url "http://groups.yahoo.com/group/AdmiralNeighborhood/", :community/neighborhood #db/id[:db.part/user -1000005]}
{:db/id #db/id[:db.part/user -1000007], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000008], :neighborhood/name "Alki", :neighborhood/district #db/id[:db.part/user -1000007]}
{:community/category ["members of the Alki Community Council and residents of the Alki Beach neighborhood"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000009], :community/name "Alki News", :community/url "http://groups.yahoo.com/group/alkibeachcommunity/", :community/neighborhood #db/id[:db.part/user -1000008]}
{:db/id #db/id[:db.part/user -1000010], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000011], :neighborhood/name "Alki"}
{:community/category ["news" "council meetings"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000012], :community/name "Alki News/Alki Community Council", :community/url "http://alkinews.wordpress.com/", :community/neighborhood #db/id[:db.part/user -1000011]}
{:district/region :region/w, :db/id #db/id[:db.part/user -1000013], :district/name "Downtown"}
{:db/id #db/id[:db.part/user -1000014], :neighborhood/name "Belltown", :neighborhood/district #db/id[:db.part/user -1000013]}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000015], :community/name "All About Belltown", :community/url "http://www.belltown.org/", :community/neighborhood #db/id[:db.part/user -1000014]}
{:district/region :region/s, :db/id #db/id[:db.part/user -1000016], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000017], :neighborhood/name "South Park", :neighborhood/district #db/id[:db.part/user -1000016]}
{:community/category ["neighborhood info"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000018], :community/name "All About South Park", :community/url "http://www.allaboutsouthpark.com/", :community/neighborhood #db/id[:db.part/user -1000017]}
{:db/id #db/id[:db.part/user -1000019], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000020], :neighborhood/name "West Seattle", :neighborhood/district #db/id[:db.part/user -1000019]}
{:community/category ["arts"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000021], :community/name "ArtsWest", :community/url "http://www.artswest.org/?q=node/28", :community/neighborhood #db/id[:db.part/user -1000020]}
{:district/region :region/nw, :db/id #db/id[:db.part/user -1000022], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000023], :neighborhood/name "Ballard", :neighborhood/district #db/id[:db.part/user -1000022]}
{:community/category ["news" "human interest"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000024], :community/name "At Large in Ballard", :community/url "http://blog.seattlepi.com/ballard/", :community/neighborhood #db/id[:db.part/user -1000023]}
{:district/region :region/ne, :db/id #db/id[:db.part/user -1000025], :district/name "Northeast"}
{:db/id #db/id[:db.part/user -1000026], :neighborhood/name "Regional Sites", :neighborhood/district #db/id[:db.part/user -1000025]}
{:community/category ["news" "traffic" "planning"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000027], :community/name "Aurora Seattle", :community/url "http://www.auroraseattle.com/", :community/neighborhood #db/id[:db.part/user -1000026]}
{:db/id #db/id[:db.part/user -1000028], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000029], :neighborhood/name "Ballard"}
{:community/category ["personal ballard-centric blog"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000030], :community/name "Ballard Avenue", :community/url "http://www.ballardavenue.blogspot.com/", :community/neighborhood #db/id[:db.part/user -1000029]}
{:db/id #db/id[:db.part/user -1000031], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000032], :neighborhood/name "Ballard"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000033], :community/name "Ballard Blog", :community/url "http://ballardblog.com/", :community/neighborhood #db/id[:db.part/user -1000032]}
{:db/id #db/id[:db.part/user -1000034], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000035], :neighborhood/name "Ballard"}
{:community/category ["chamber of commerce"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000036], :community/name "Ballard Chamber of Commerce", :community/url "http://www.ballardchamber.com/", :community/neighborhood #db/id[:db.part/user -1000035]}
{:db/id #db/id[:db.part/user -1000037], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000038], :neighborhood/name "Ballard"}
{:community/category ["district council"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000039], :community/name "Ballard District Council", :community/url "http://www.ballarddistrictcouncil.org/", :community/neighborhood #db/id[:db.part/user -1000038]}
{:db/id #db/id[:db.part/user -1000040], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000041], :neighborhood/name "Ballard"}
{:community/category ["restaurants" "nightlift" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000042], :community/name "Ballard Gossip Girl", :community/url "http://www.ballardgossipgirl.com/", :community/neighborhood #db/id[:db.part/user -1000041]}
{:db/id #db/id[:db.part/user -1000043], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000044], :neighborhood/name "Ballard"}
{:community/category ["historical society"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000045], :community/name "Ballard Historical Society", :community/url "http://www.ballardhistory.org/", :community/neighborhood #db/id[:db.part/user -1000044]}
{:db/id #db/id[:db.part/user -1000046], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000047], :neighborhood/name "Ballard"}
{:community/category ["Ballard parents"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000048], :community/name "Ballard Moms", :community/url "http://groups.yahoo.com/group/BallardMoms/", :community/neighborhood #db/id[:db.part/user -1000047]}
{:db/id #db/id[:db.part/user -1000049], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000050], :neighborhood/name "Ballard"}
{:community/category ["neighborhood residents"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000051], :community/name "Ballard Neighbor Connection", :community/url "http://groups.yahoo.com/group/BallardNeighborConnection/", :community/neighborhood #db/id[:db.part/user -1000050]}
{:db/id #db/id[:db.part/user -1000052], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000053], :neighborhood/name "Ballard"}
{:community/category ["news" "personal"], :community/orgtype :community.orgtype/personal, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000054], :community/name "ballardite blog", :community/url "http://www.ballardite.blogspot.com/", :community/neighborhood #db/id[:db.part/user -1000053]}
{:db/id #db/id[:db.part/user -1000055], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000056], :neighborhood/name "Alki"}
{:community/category ["news" "home sales" "crime"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000057], :community/name "Beach Drive Blog", :community/url "http://www.mortgageporter.com/beach_drive/", :community/neighborhood #db/id[:db.part/user -1000056]}
{:db/id #db/id[:db.part/user -1000058], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000059], :neighborhood/name "Beacon Hill", :neighborhood/district #db/id[:db.part/user -1000058]}
{:community/category ["community" "public safety"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000060], :community/name "Beacon Hill Alliance of Neighbors", :community/url "http://www.cityofseattle.net/ban/", :community/neighborhood #db/id[:db.part/user -1000059]}
{:db/id #db/id[:db.part/user -1000061], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000062], :neighborhood/name "Beacon Hill"}
{:community/category ["news" "events" "food" "nightlife" "criminal activity"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000063], :community/name "Beacon Hill Blog", :community/url "http://beaconhill.seattle.wa.us/", :community/neighborhood #db/id[:db.part/user -1000062]}
{:db/id #db/id[:db.part/user -1000064], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000065], :neighborhood/name "Beacon Hill"}
{:community/category ["criminal activity"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000066], :community/name "Beacon Hill Burglaries", :community/url "http://maps.google.com/maps/ms?ie=UTF8&hl=en&msa=0&msid=107398592337461190820.000449fcf97ff8bfbe281&z=14or", :community/neighborhood #db/id[:db.part/user -1000065]}
{:db/id #db/id[:db.part/user -1000067], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000068], :neighborhood/name "Beacon Hill"}
{:community/category ["news" "announcements" "community concerns"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/wiki, :db/id #db/id[:db.part/user -1000069], :community/name "Beacon Hill Community Site", :community/url "http://beaconhillcommunity.wetpaint.com/", :community/neighborhood #db/id[:db.part/user -1000068]}
{:db/id #db/id[:db.part/user -1000070], :district/name "Downtown"}
{:db/id #db/id[:db.part/user -1000071], :neighborhood/name "Belltown"}
{:community/category ["news" "events"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000072], :community/name "belltown", :community/url "http://www.belltownpeople.com/", :community/neighborhood #db/id[:db.part/user -1000071]}
{:district/region :region/se, :db/id #db/id[:db.part/user -1000073], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000074], :neighborhood/name "Columbia City", :neighborhood/district #db/id[:db.part/user -1000073]}
{:community/category ["community group"], :community/type :community.type/website, :db/id #db/id[:db.part/user -1000075], :community/name "Bike Works!", :community/url "http://www.bikeworks.org/", :community/neighborhood #db/id[:db.part/user -1000074]}
{:db/id #db/id[:db.part/user -1000076], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000077], :neighborhood/name "Georgetown", :neighborhood/district #db/id[:db.part/user -1000076]}
{:community/category ["news" "events" "911 blotter"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000078], :community/name "Blogging Georgetown", :community/url "http://www.blogginggeorgetown.com/", :community/neighborhood #db/id[:db.part/user -1000077]}
{:db/id #db/id[:db.part/user -1000079], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000080], :neighborhood/name "Georgetown"}
{:community/category ["news" "events" "911 blotter"], :community/orgtype :community.orgtype/community, :community/type :community.type/facebook-page, :db/id #db/id[:db.part/user -1000081], :community/name "Blogging Georgetown", :community/url "http://www.facebook.com/blogginggeorgetown", :community/neighborhood #db/id[:db.part/user -1000080]}
{:district/region :region/sw, :db/id #db/id[:db.part/user -1000082], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000083], :neighborhood/name "Broadview", :neighborhood/district #db/id[:db.part/user -1000082]}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000084], :community/name "Broadview Community Council", :community/url "http://groups.google.com/group/broadview-community-council", :community/neighborhood #db/id[:db.part/user -1000083]}
{:db/id #db/id[:db.part/user -1000085], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000086], :neighborhood/name "Broadview"}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000087], :community/name "Broadview Community Council", :community/url "http://www.broadviewseattle.org/", :community/neighborhood #db/id[:db.part/user -1000086]}
{:db/id #db/id[:db.part/user -1000088], :district/name "East"}
{:db/id #db/id[:db.part/user -1000089], :neighborhood/name "Capitol Hill"}
{:community/category ["community council" "news" "events"], :community/orgtype :community.orgtype/community, :community/type :community.type/wiki, :db/id #db/id[:db.part/user -1000090], :community/name "Capitol Hill Community Council", :community/url "http://chcc.wikidot.com/", :community/neighborhood #db/id[:db.part/user -1000089]}
{:db/id #db/id[:db.part/user -1000091], :district/name "East"}
{:db/id #db/id[:db.part/user -1000092], :neighborhood/name "Capitol Hill"}
{:community/category ["affordable housing"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000093], :community/name "Capitol Hill Housing", :community/url "http://capitolhillhousing.org/", :community/neighborhood #db/id[:db.part/user -1000092]}
{:db/id #db/id[:db.part/user -1000094], :district/name "East"}
{:db/id #db/id[:db.part/user -1000095], :neighborhood/name "Capitol Hill"}
{:community/category ["local miscellany"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000096], :community/name "Capitol Hill Triangle", :community/url "http://chtriangle.blogspot.com/", :community/neighborhood #db/id[:db.part/user -1000095]}
{:district/region :region/e, :db/id #db/id[:db.part/user -1000097], :district/name "Central"}
{:db/id #db/id[:db.part/user -1000098], :neighborhood/name "Central District", :neighborhood/district #db/id[:db.part/user -1000097]}
{:community/category ["festival planning association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000099], :community/name "Central Area Community Festival Association", :community/url "http://www.cacf.com/", :community/neighborhood #db/id[:db.part/user -1000098]}
{:db/id #db/id[:db.part/user -1000100], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000101], :neighborhood/name "Ballard"}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000102], :community/name "Central Ballard Community Council", :community/url "http://www.neighborhoodlink.com/seattle/cbcc/", :community/neighborhood #db/id[:db.part/user -1000101]}
{:db/id #db/id[:db.part/user -1000103], :district/name "Central"}
{:db/id #db/id[:db.part/user -1000104], :neighborhood/name "Central District"}
{:community/category ["news" "events" "food" "drink" "reviews"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000105], :community/name "Central District News", :community/url "http://www.centraldistrictnews.com/", :community/neighborhood #db/id[:db.part/user -1000104]}
{:db/id #db/id[:db.part/user -1000106], :district/name "Downtown"}
{:db/id #db/id[:db.part/user -1000107], :neighborhood/name "International District", :neighborhood/district #db/id[:db.part/user -1000106]}
{:community/category ["business improvement association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000108], :community/name "Chinatown/International District", :community/url "http://www.cidbia.org/", :community/neighborhood #db/id[:db.part/user -1000107]}
{:db/id #db/id[:db.part/user -1000109], :district/name "Downtown"}
{:db/id #db/id[:db.part/user -1000110], :neighborhood/name "International District"}
{:community/category ["referral" "advocacy" "support services"], :community/orgtype :community.orgtype/nonprofit, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000111], :community/name "Chinese Information and Service Center", :community/url "http://www.cisc-seattle.org/", :community/neighborhood #db/id[:db.part/user -1000110]}
{:db/id #db/id[:db.part/user -1000112], :district/name "East"}
{:db/id #db/id[:db.part/user -1000113], :neighborhood/name "Capitol Hill"}
{:community/category ["news" "events" "food" "drink" "criminal activity"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000114], :community/name "CHS Capitol Hill Seattle Blog", :community/url "http://www.capitolhillseattle.com/", :community/neighborhood #db/id[:db.part/user -1000113]}
{:db/id #db/id[:db.part/user -1000115], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000116], :neighborhood/name "Columbia City"}
{:community/category ["neighborhood community site; buying/selling" "restaurants" "favorite places" "etc."], :community/orgtype :community.orgtype/community, :community/type :community.type/wiki, :db/id #db/id[:db.part/user -1000117], :community/name "Columbia Citizens", :community/url "http://columbiacitizens.net/", :community/neighborhood #db/id[:db.part/user -1000116]}
{:db/id #db/id[:db.part/user -1000118], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000119], :neighborhood/name "Columbia City"}
{:community/category ["twitter for Columbia Citizens"], :community/orgtype :community.orgtype/community, :community/type :community.type/twitter, :db/id #db/id[:db.part/user -1000120], :community/name "Columbia Citizens", :community/url "http://twitter.com/CCitizens", :community/neighborhood #db/id[:db.part/user -1000119]}
{:db/id #db/id[:db.part/user -1000121], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000122], :neighborhood/name "Columbia City"}
{:community/category ["neighborhood community site; buying/selling" "restaurants" "favorite places" "etc."], :community/orgtype :community.orgtype/community, :community/type :community.type/facebook-page, :db/id #db/id[:db.part/user -1000123], :community/name "Columbia Citizens", :community/url "http://www.facebook.com/pages/Columbia-Citizens/48558627705", :community/neighborhood #db/id[:db.part/user -1000122]}
{:db/id #db/id[:db.part/user -1000124], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000125], :neighborhood/name "Columbia City"}
{:community/category ["news" "events" "food" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000126], :community/name "Columbia City Blog", :community/url "http://www.columbiacityblog.com/", :community/neighborhood #db/id[:db.part/user -1000125]}
{:db/id #db/id[:db.part/user -1000127], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000128], :neighborhood/name "Columbia City"}
{:community/category ["business association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000129], :community/name "Columbia City, Seattle", :community/url "http://www.columbiacityseattle.com/", :community/neighborhood #db/id[:db.part/user -1000128]}
{:db/id #db/id[:db.part/user -1000130], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000131], :neighborhood/name "Rainier Valley", :neighborhood/district #db/id[:db.part/user -1000130]}
{:community/category ["portal"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000132], :community/name "Columbia City: Rainier Valley", :community/url "http://www.rainiervalley.org/", :community/neighborhood #db/id[:db.part/user -1000131]}
{:district/region :region/sw, :db/id #db/id[:db.part/user -1000133], :district/name "Delridge"}
{:db/id #db/id[:db.part/user -1000134], :neighborhood/name "West Seattle"}
{:community/category ["sustainable food"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000135], :community/name "Community Harvest of Southwest Seattle", :community/url "http://gleanit.org", :community/neighborhood #db/id[:db.part/user -1000134]}
{:db/id #db/id[:db.part/user -1000136], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000137], :neighborhood/name "Crown Hill", :neighborhood/district #db/id[:db.part/user -1000136]}
{:community/category ["neighborhood-issues" "neighborhood-planning" "news"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000138], :community/name "Crown Hill Neighbors", :community/url "http://www.crownhillneighbors.org", :community/neighborhood #db/id[:db.part/user -1000137]}
{:db/id #db/id[:db.part/user -1000139], :district/name "Delridge"}
{:db/id #db/id[:db.part/user -1000140], :neighborhood/name "Delridge", :neighborhood/district #db/id[:db.part/user -1000139]}
{:community/category ["community organization"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000141], :community/name "Delridge Grassroots Leadership", :community/url "http://delridge.blogspot.com/", :community/neighborhood #db/id[:db.part/user -1000140]}
{:db/id #db/id[:db.part/user -1000142], :district/name "Delridge"}
{:db/id #db/id[:db.part/user -1000143], :neighborhood/name "Delridge"}
{:community/category ["neighborhood association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000144], :community/name "Delridge Neighborhoods Development Association", :community/url "http://www.dnda.org/", :community/neighborhood #db/id[:db.part/user -1000143]}
{:db/id #db/id[:db.part/user -1000145], :district/name "Delridge"}
{:db/id #db/id[:db.part/user -1000146], :neighborhood/name "Delridge"}
{:community/category ["produce coop"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000147], :community/name "Delridge Produce Cooperative", :community/url "http://sites.google.com/site/delridgeproducecooperative/", :community/neighborhood #db/id[:db.part/user -1000146]}
{:district/region :region/w, :db/id #db/id[:db.part/user -1000148], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000149], :neighborhood/name "South Lake Union", :neighborhood/district #db/id[:db.part/user -1000148]}
{:community/category ["news" "events" "shopping" "dining"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000150], :community/name "Discover SLU", :community/url "http://www.discoverslu.com", :community/neighborhood #db/id[:db.part/user -1000149]}
{:db/id #db/id[:db.part/user -1000151], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000152], :neighborhood/name "South Lake Union"}
{:community/category ["news" "events" "shopping" "dining"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/facebook-page, :db/id #db/id[:db.part/user -1000153], :community/name "Discover SLU", :community/url "http://www.facebook.com/discoverslu", :community/neighborhood #db/id[:db.part/user -1000152]}
{:db/id #db/id[:db.part/user -1000154], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000155], :neighborhood/name "South Lake Union"}
{:community/category ["news" "events" "shopping" "dining"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/twitter, :db/id #db/id[:db.part/user -1000156], :community/name "Discover SLU", :community/url "http://www.twitter.com/southlakeunion", :community/neighborhood #db/id[:db.part/user -1000155]}
{:db/id #db/id[:db.part/user -1000157], :district/name "Downtown"}
{:db/id #db/id[:db.part/user -1000158], :neighborhood/name "Downtown", :neighborhood/district #db/id[:db.part/user -1000157]}
{:community/category ["news and events"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000159], :community/name "Downtown Dispatch", :community/url "http://downtowndispatch.com/", :community/neighborhood #db/id[:db.part/user -1000158]}
{:db/id #db/id[:db.part/user -1000160], :district/name "Downtown"}
{:db/id #db/id[:db.part/user -1000161], :neighborhood/name "Downtown"}
{:community/category ["business association"], :community/type :community.type/website, :db/id #db/id[:db.part/user -1000162], :community/name "Downtown Seattle Association", :community/url "http://www.downtownseattle.com/", :community/neighborhood #db/id[:db.part/user -1000161]}
{:db/id #db/id[:db.part/user -1000163], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000164], :neighborhood/name "Ballard"}
{:community/category ["community association" "news" "events" "meeting"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000165], :community/name "East Ballard Community Association Blog", :community/url "http://eastballard.wordpress.com/", :community/neighborhood #db/id[:db.part/user -1000164]}
{:db/id #db/id[:db.part/user -1000166], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000167], :neighborhood/name "Eastlake", :neighborhood/district #db/id[:db.part/user -1000166]}
{:community/category ["news" "traffic" "events" "criminal activity"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000168], :community/name "Eastlake Ave. ", :community/url "http://eastlakeave.neighborlogs.com/", :community/neighborhood #db/id[:db.part/user -1000167]}
{:db/id #db/id[:db.part/user -1000169], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000170], :neighborhood/name "Eastlake"}
{:community/category ["updates on blog posts"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000171], :community/name "Eastlake Ave. ", :community/url "http://twitter.com/eastlakeave", :community/neighborhood #db/id[:db.part/user -1000170]}
{:db/id #db/id[:db.part/user -1000172], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000173], :neighborhood/name "Eastlake"}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000174], :community/name "Eastlake Community Council", :community/url "http://www.eastlakeseattle.org/", :community/neighborhood #db/id[:db.part/user -1000173]}
{:db/id #db/id[:db.part/user -1000175], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000176], :neighborhood/name "Eastlake"}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/facebook-page, :db/id #db/id[:db.part/user -1000177], :community/name "Eastlake Community Council", :community/url "http://www.facebook.com/group.php?gid=7145111961", :community/neighborhood #db/id[:db.part/user -1000176]}
{:db/id #db/id[:db.part/user -1000178], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000179], :neighborhood/name "Fauntleroy", :neighborhood/district #db/id[:db.part/user -1000178]}
{:community/category ["community association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000180], :community/name "Fauntleroy Community Association", :community/url "http://fauntleroy.net/", :community/neighborhood #db/id[:db.part/user -1000179]}
{:db/id #db/id[:db.part/user -1000181], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000182], :neighborhood/name "Fauntleroy"}
{:community/category ["community assocation"], :community/orgtype :community.orgtype/community, :community/type :community.type/facebook-page, :db/id #db/id[:db.part/user -1000183], :community/name "Fauntleroy Community Association", :community/url "http://www.facebook.com/pages/Seattle-WA/Fauntleroy-Community-Association/63181596775?v=wall&viewas=1779772562&ref=ts", :community/neighborhood #db/id[:db.part/user -1000182]}
{:db/id #db/id[:db.part/user -1000184], :district/name "East"}
{:db/id #db/id[:db.part/user -1000185], :neighborhood/name "First Hill", :neighborhood/district #db/id[:db.part/user -1000184]}
{:community/category ["neighborhood association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000186], :community/name "First Hill Improvement Association", :community/url "http://www.firsthill.org/", :community/neighborhood #db/id[:db.part/user -1000185]}
{:db/id #db/id[:db.part/user -1000187], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000188], :neighborhood/name "Fremont", :neighborhood/district #db/id[:db.part/user -1000187]}
{:community/category ["fremont arts council members"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000189], :community/name "Fremont Arts Council", :community/url "http://groups.yahoo.com/group/fremontartscouncil/", :community/neighborhood #db/id[:db.part/user -1000188]}
{:db/id #db/id[:db.part/user -1000190], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000191], :neighborhood/name "Fremont"}
{:community/category ["communtiy group"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000192], :community/name "Fremont Arts Council", :community/url "http://www.fremontartscouncil.org/", :community/neighborhood #db/id[:db.part/user -1000191]}
{:db/id #db/id[:db.part/user -1000193], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000194], :neighborhood/name "Fremont"}
{:community/category ["chamber of commerce"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000195], :community/name "Fremont Chamber of Commerce", :community/url "http://www.fremontseattle.com/", :community/neighborhood #db/id[:db.part/user -1000194]}
{:db/id #db/id[:db.part/user -1000196], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000197], :neighborhood/name "Fremont"}
{:community/category ["news" "events" "food" "reviews"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/twitter, :db/id #db/id[:db.part/user -1000198], :community/name "Fremont Universe", :community/url "http://twitter.com/fremontuniverse", :community/neighborhood #db/id[:db.part/user -1000197]}
{:db/id #db/id[:db.part/user -1000199], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000200], :neighborhood/name "Fremont"}
{:community/category ["news" "events" "food" "reviews"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/facebook-page, :db/id #db/id[:db.part/user -1000201], :community/name "Fremont Universe", :community/url "http://www.facebook.com/pages/Fremont-Universe-Seattle/88279594341?ref=s", :community/neighborhood #db/id[:db.part/user -1000200]}
{:db/id #db/id[:db.part/user -1000202], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000203], :neighborhood/name "Fremont"}
{:community/category ["news" "events" "food" "reviews"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000204], :community/name "Fremont Universe", :community/url "http://www.fremontuniverse.com/", :community/neighborhood #db/id[:db.part/user -1000203]}
{:district/region :region/w, :db/id #db/id[:db.part/user -1000205], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000206], :neighborhood/name "Magnolia", :neighborhood/district #db/id[:db.part/user -1000205]}
{:community/category ["park issues"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000207], :community/name "Friends of Discovery Park", :community/url "http://www.friendsdiscoverypark.org/", :community/neighborhood #db/id[:db.part/user -1000206]}
{:db/id #db/id[:db.part/user -1000208], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000209], :neighborhood/name "Fremont"}
{:community/category ["community organization"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000210], :community/name "Friends of Fremont Peak Park", :community/url "http://www.fremontpeakpark.org/", :community/neighborhood #db/id[:db.part/user -1000209]}
{:db/id #db/id[:db.part/user -1000211], :district/name "Central"}
{:db/id #db/id[:db.part/user -1000212], :neighborhood/name "Central District"}
{:community/category ["park issues"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000213], :community/name "Friends of Frink Park", :community/url "http://www.frinkpark.org/", :community/neighborhood #db/id[:db.part/user -1000212]}
{:db/id #db/id[:db.part/user -1000214], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000215], :neighborhood/name "Green Lake", :neighborhood/district #db/id[:db.part/user -1000214]}
{:community/category ["neighborhood group"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000216], :community/name "Friends of Green Lake", :community/url "http://www.friendsofgreenlake.org/", :community/neighborhood #db/id[:db.part/user -1000215]}
{:db/id #db/id[:db.part/user -1000217], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000218], :neighborhood/name "Seward Park", :neighborhood/district #db/id[:db.part/user -1000217]}
{:community/category ["park issues"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000219], :community/name "Friends of Seward Park", :community/url "http://www.sewardpark.org/", :community/neighborhood #db/id[:db.part/user -1000218]}
{:db/id #db/id[:db.part/user -1000220], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000221], :neighborhood/name "Genesee-Schmitz", :neighborhood/district #db/id[:db.part/user -1000220]}
{:community/category ["neighborhood council"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000222], :community/name "Genesee-Schmitz Neighborhood Council", :community/url "http://genesee-schmitz.org/", :community/neighborhood #db/id[:db.part/user -1000221]}
{:db/id #db/id[:db.part/user -1000223], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000224], :neighborhood/name "Georgetown"}
{:community/category ["community arts"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000225], :community/name "Georgetown Art Center", :community/url "http://georgetownartcenter.blogspot.com/", :community/neighborhood #db/id[:db.part/user -1000224]}
{:db/id #db/id[:db.part/user -1000226], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000227], :neighborhood/name "Georgetown"}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000228], :community/name "Georgetown Neighborhood", :community/url "http://www.georgetownneighborhood.com/", :community/neighborhood #db/id[:db.part/user -1000227]}
{:db/id #db/id[:db.part/user -1000229], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000230], :neighborhood/name "Georgetown"}
{:community/category ["community members"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000231], :community/name "Georgetown Seattle", :community/url "http://groups.yahoo.com/group/Georgetown-Seattle/", :community/neighborhood #db/id[:db.part/user -1000230]}
{:db/id #db/id[:db.part/user -1000232], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000233], :neighborhood/name "Green Lake"}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000234], :community/name "Greenlake Community Council", :community/url "http://www.greenlakecommunitycouncil.org/", :community/neighborhood #db/id[:db.part/user -1000233]}
{:db/id #db/id[:db.part/user -1000235], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000236], :neighborhood/name "Green Lake"}
{:community/category ["events" "for sale" "services"], :community/orgtype :community.orgtype/community, :community/type :community.type/wiki, :db/id #db/id[:db.part/user -1000237], :community/name "Greenlake Community Wiki", :community/url "http://greenlake.wetpaint.com/", :community/neighborhood #db/id[:db.part/user -1000236]}
{:db/id #db/id[:db.part/user -1000238], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000239], :neighborhood/name "Greenwood", :neighborhood/district #db/id[:db.part/user -1000238]}
{:community/category ["neighborhood association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000240], :community/name "Greenwood Aurora Involved Neighbors", :community/url "http://www.gainseattle.com/", :community/neighborhood #db/id[:db.part/user -1000239]}
{:db/id #db/id[:db.part/user -1000241], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000242], :neighborhood/name "Greenwood"}
{:community/category ["news" "events"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000243], :community/name "Greenwood Blog", :community/url "http://www.greenwoodblog.com/", :community/neighborhood #db/id[:db.part/user -1000242]}
{:db/id #db/id[:db.part/user -1000244], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000245], :neighborhood/name "Greenwood"}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000246], :community/name "Greenwood Community Council", :community/url "http://www.greenwoodcommunitycouncil.org/about/", :community/neighborhood #db/id[:db.part/user -1000245]}
{:db/id #db/id[:db.part/user -1000247], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000248], :neighborhood/name "Greenwood"}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000249], :community/name "Greenwood Community Council Announcements", :community/url "http://groups.yahoo.com/group/Greenwood_News/", :community/neighborhood #db/id[:db.part/user -1000248]}
{:db/id #db/id[:db.part/user -1000250], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000251], :neighborhood/name "Greenwood"}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000252], :community/name "Greenwood Community Council Discussion", :community/url "http://groups.yahoo.com/group/greenwood-discussion/", :community/neighborhood #db/id[:db.part/user -1000251]}
{:db/id #db/id[:db.part/user -1000253], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000254], :neighborhood/name "Greenwood"}
{:community/category ["chamber of commerce"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000255], :community/name "Greenwood Phinney Chamber of Commerce", :community/url "http://www.greenwood-phinney.com/", :community/neighborhood #db/id[:db.part/user -1000254]}
{:district/region :region/n, :db/id #db/id[:db.part/user -1000256], :district/name "North"}
{:db/id #db/id[:db.part/user -1000257], :neighborhood/name "Haller Lake", :neighborhood/district #db/id[:db.part/user -1000256]}
{:community/category ["community organization"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000258], :community/name "Haller Lake Community Club", :community/url "http://www.hallerlake.info/", :community/neighborhood #db/id[:db.part/user -1000257]}
{:db/id #db/id[:db.part/user -1000259], :district/name "Northeast"}
{:db/id #db/id[:db.part/user -1000260], :neighborhood/name "Hawthorne Hills", :neighborhood/district #db/id[:db.part/user -1000259]}
{:community/category ["community council"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000261], :community/name "Hawthorne Hills Community Website", :community/url "http://www.seattle.gov/hawthornehills/", :community/neighborhood #db/id[:db.part/user -1000260]}
{:db/id #db/id[:db.part/user -1000262], :district/name "Delridge"}
{:db/id #db/id[:db.part/user -1000263], :neighborhood/name "Highland Park", :neighborhood/district #db/id[:db.part/user -1000262]}
{:community/category ["community group"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000264], :community/name "Highland Park Action Committee", :community/url "http://www.highlandpk.net/", :community/neighborhood #db/id[:db.part/user -1000263]}
{:db/id #db/id[:db.part/user -1000265], :district/name "Delridge"}
{:db/id #db/id[:db.part/user -1000266], :neighborhood/name "Highland Park"}
{:community/category ["neighborhood group"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000267], :community/name "Highland Park Improvement Club", :community/url "http://www.hpic1919.org/", :community/neighborhood #db/id[:db.part/user -1000266]}
{:db/id #db/id[:db.part/user -1000268], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000269], :neighborhood/name "Ballard"}
{:community/category ["food" "nightlife" "shopping" "services"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000270], :community/name "InBallard", :community/url "http://inballard.com/", :community/neighborhood #db/id[:db.part/user -1000269]}
{:db/id #db/id[:db.part/user -1000271], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000272], :neighborhood/name "Interbay", :neighborhood/district #db/id[:db.part/user -1000271]}
{:community/category ["news" "sports"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000273], :community/name "Interbay District Blog", :community/url "http://interbayneighborhood.neighborlogs.com/", :community/neighborhood #db/id[:db.part/user -1000272]}
{:db/id #db/id[:db.part/user -1000274], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000275], :neighborhood/name "Interbay"}
{:community/category ["neighborhood association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000276], :community/name "Interbay Neighborhood Association", :community/url "http://www.our-interbay.org/", :community/neighborhood #db/id[:db.part/user -1000275]}
{:db/id #db/id[:db.part/user -1000277], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000278], :neighborhood/name "Beacon Hill"}
{:community/category ["community"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000279], :community/name "Jefferson Park Alliance", :community/url "http://www.cityofseattle.net/commnty/Beacon/groups/jpa/", :community/neighborhood #db/id[:db.part/user -1000278]}
{:db/id #db/id[:db.part/user -1000280], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000281], :neighborhood/name "Junction", :neighborhood/district #db/id[:db.part/user -1000280]}
{:community/category ["events" "meetings"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000282], :community/name "Junction Neighborhood Organization", :community/url "http://www.wsjuno.com/", :community/neighborhood #db/id[:db.part/user -1000281]}
{:db/id #db/id[:db.part/user -1000283], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000284], :neighborhood/name "Ballard"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000285], :community/name "KOMO Communities - Ballard", :community/url "http://ballard.komonews.com/", :community/neighborhood #db/id[:db.part/user -1000284]}
{:db/id #db/id[:db.part/user -1000286], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000287], :neighborhood/name "Beacon Hill"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000288], :community/name "KOMO Communities - Beacon Hill", :community/url "http://beaconhill.komonews.com", :community/neighborhood #db/id[:db.part/user -1000287]}
{:db/id #db/id[:db.part/user -1000289], :district/name "East"}
{:db/id #db/id[:db.part/user -1000290], :neighborhood/name "Capitol Hill"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000291], :community/name "KOMO Communities - Captol Hill", :community/url "http://capitolhill.komonews.com", :community/neighborhood #db/id[:db.part/user -1000290]}
{:db/id #db/id[:db.part/user -1000292], :district/name "East"}
{:db/id #db/id[:db.part/user -1000293], :neighborhood/name "Central District"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000294], :community/name "KOMO Communities - Central District", :community/url "http://centraldistrict.komonews.com", :community/neighborhood #db/id[:db.part/user -1000293]}
{:db/id #db/id[:db.part/user -1000295], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000296], :neighborhood/name "Columbia City"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000297], :community/name "KOMO Communities - Columbia City", :community/url "http://columbiacity.komonews.com", :community/neighborhood #db/id[:db.part/user -1000296]}
{:db/id #db/id[:db.part/user -1000298], :district/name "Downtown"}
{:db/id #db/id[:db.part/user -1000299], :neighborhood/name "Downtown"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000300], :community/name "KOMO Communities - Downtown", :community/url "http://downtown.komonews.com", :community/neighborhood #db/id[:db.part/user -1000299]}
{:db/id #db/id[:db.part/user -1000301], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000302], :neighborhood/name "Fremont"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000303], :community/name "KOMO Communities - Fremont", :community/url "http://fremont.komonews.com", :community/neighborhood #db/id[:db.part/user -1000302]}
{:db/id #db/id[:db.part/user -1000304], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000305], :neighborhood/name "Georgetown"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000306], :community/name "KOMO Communities - Georgetown", :community/url "http://georgetown.komonews.com", :community/neighborhood #db/id[:db.part/user -1000305]}
{:db/id #db/id[:db.part/user -1000307], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000308], :neighborhood/name "Green Lake"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000309], :community/name "KOMO Communities - Green Lake", :community/url "http://greenlake.komonews.com", :community/neighborhood #db/id[:db.part/user -1000308]}
{:db/id #db/id[:db.part/user -1000310], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000311], :neighborhood/name "Greenwood-Phinney", :neighborhood/district #db/id[:db.part/user -1000310]}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000312], :community/name "KOMO Communities - Greenwood-Phinney", :community/url "http://greenwood-phinney.komonews.com", :community/neighborhood #db/id[:db.part/user -1000311]}
{:db/id #db/id[:db.part/user -1000313], :district/name "North"}
{:db/id #db/id[:db.part/user -1000314], :neighborhood/name "Lake City", :neighborhood/district #db/id[:db.part/user -1000313]}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000315], :community/name "KOMO Communities - Lake City", :community/url "http://lakecity.komonews.com", :community/neighborhood #db/id[:db.part/user -1000314]}
{:db/id #db/id[:db.part/user -1000316], :district/name "East"}
{:db/id #db/id[:db.part/user -1000317], :neighborhood/name "Madison Park", :neighborhood/district #db/id[:db.part/user -1000316]}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000318], :community/name "KOMO Communities - Madison Park", :community/url "http://madisonpark.komonews.com", :community/neighborhood #db/id[:db.part/user -1000317]}
{:db/id #db/id[:db.part/user -1000319], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000320], :neighborhood/name "Magnolia"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000321], :community/name "KOMO Communities - Magnolia", :community/url "http://magnolia.komonews.com", :community/neighborhood #db/id[:db.part/user -1000320]}
{:db/id #db/id[:db.part/user -1000322], :district/name "North"}
{:db/id #db/id[:db.part/user -1000323], :neighborhood/name "North Seattle", :neighborhood/district #db/id[:db.part/user -1000322]}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000324], :community/name "KOMO Communities - North Seattle", :community/url "http://northseattle.komonews.com", :community/neighborhood #db/id[:db.part/user -1000323]}
{:db/id #db/id[:db.part/user -1000325], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000326], :neighborhood/name "Queen Anne", :neighborhood/district #db/id[:db.part/user -1000325]}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000327], :community/name "KOMO Communities - Queen Anne", :community/url "http://queenanne.komonews.com", :community/neighborhood #db/id[:db.part/user -1000326]}
{:db/id #db/id[:db.part/user -1000328], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000329], :neighborhood/name "Rainier Valley"}
{:community/category ["news" "events" "human interest" "criminal activity" "food" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000330], :community/name "KOMO Communities - Rainier Valley", :community/url "http://rainiervalley.komonews.com", :community/neighborhood #db/id[:db.part/user -1000329]}
{:db/id #db/id[:db.part/user -1000331], :district/name "Lake Union"}
{:db/id #db/id[:db.part/user -1000332], :neighborhood/name "South Lake Union"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000333], :community/name "KOMO Communities - South Lake Union", :community/url "http://southlakeunion.komonews.com", :community/neighborhood #db/id[:db.part/user -1000332]}
{:db/id #db/id[:db.part/user -1000334], :district/name "Northeast"}
{:db/id #db/id[:db.part/user -1000335], :neighborhood/name "University District", :neighborhood/district #db/id[:db.part/user -1000334]}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000336], :community/name "KOMO Communities - U-District", :community/url "http://udistrict.komonews.com", :community/neighborhood #db/id[:db.part/user -1000335]}
{:db/id #db/id[:db.part/user -1000337], :district/name "Northeast"}
{:db/id #db/id[:db.part/user -1000338], :neighborhood/name "View Ridge", :neighborhood/district #db/id[:db.part/user -1000337]}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000339], :community/name "KOMO Communities - View Ridge", :community/url "http://viewridge.komonews.com", :community/neighborhood #db/id[:db.part/user -1000338]}
{:db/id #db/id[:db.part/user -1000340], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000341], :neighborhood/name "Wallingford", :neighborhood/district #db/id[:db.part/user -1000340]}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000342], :community/name "KOMO Communities - Wallingford", :community/url "http://wallingford.komonews.com", :community/neighborhood #db/id[:db.part/user -1000341]}
{:db/id #db/id[:db.part/user -1000343], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000344], :neighborhood/name "West Seattle"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000345], :community/name "KOMO Communities - West Seattle", :community/url "http://westseattle.komonews.com", :community/neighborhood #db/id[:db.part/user -1000344]}
{:db/id #db/id[:db.part/user -1000346], :district/name "North"}
{:db/id #db/id[:db.part/user -1000347], :neighborhood/name "Lake City"}
{:community/category ["news" "criminal activity" "food" "drink" "reviews"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000348], :community/name "Lake City Live", :community/url "http://www.lakecitylive.net/", :community/neighborhood #db/id[:db.part/user -1000347]}
{:db/id #db/id[:db.part/user -1000349], :district/name "Northeast"}
{:db/id #db/id[:db.part/user -1000350], :neighborhood/name "Laurelhurst", :neighborhood/district #db/id[:db.part/user -1000349]}
{:community/category ["community club"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000351], :community/name "Laurelhurst Community Club", :community/url "http://www.laurelhurstcc.com/", :community/neighborhood #db/id[:db.part/user -1000350]}
{:db/id #db/id[:db.part/user -1000352], :district/name "Central"}
{:db/id #db/id[:db.part/user -1000353], :neighborhood/name "Leschi", :neighborhood/district #db/id[:db.part/user -1000352]}
{:community/category ["planning" "meeting times"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000354], :community/name "Leschi Community Council", :community/url "http://groups.google.com/group/LeschiCC?hl=en", :community/neighborhood #db/id[:db.part/user -1000353]}
{:db/id #db/id[:db.part/user -1000355], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000356], :neighborhood/name "Licton Springs", :neighborhood/district #db/id[:db.part/user -1000355]}
{:community/category ["neighborhood council"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000357], :community/name "Licton Springs Neighborhood ", :community/url "http://www.lictonsprings.org/", :community/neighborhood #db/id[:db.part/user -1000356]}
{:db/id #db/id[:db.part/user -1000358], :district/name "Delridge"}
{:db/id #db/id[:db.part/user -1000359], :neighborhood/name "Delridge"}
{:community/category ["watershed info"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000360], :community/name "Longfellow Creek Community Website", :community/url "http://www.longfellowcreek.org/", :community/neighborhood #db/id[:db.part/user -1000359]}
{:db/id #db/id[:db.part/user -1000361], :district/name "East"}
{:db/id #db/id[:db.part/user -1000362], :neighborhood/name "Madison Park"}
{:community/category ["news" "housing prices" "events"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000363], :community/name "Madison Park Blogger", :community/url "http://madisonparkblogger.blogspot.com/", :community/neighborhood #db/id[:db.part/user -1000362]}
{:db/id #db/id[:db.part/user -1000364], :district/name "East"}
{:db/id #db/id[:db.part/user -1000365], :neighborhood/name "Madison Park"}
{:community/category ["business association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000366], :community/name "Madison Park Business Association", :community/url "http://www.madisonparkseattle.com/", :community/neighborhood #db/id[:db.part/user -1000365]}
{:db/id #db/id[:db.part/user -1000367], :district/name "Central"}
{:db/id #db/id[:db.part/user -1000368], :neighborhood/name "Madrona", :neighborhood/district #db/id[:db.part/user -1000367]}
{:community/category ["community group (moms)"], :community/orgtype :community.orgtype/community, :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000369], :community/name "Madrona Moms", :community/url "http://health.groups.yahoo.com/group/MadronaMoms/", :community/neighborhood #db/id[:db.part/user -1000368]}
{:db/id #db/id[:db.part/user -1000370], :district/name "Central"}
{:db/id #db/id[:db.part/user -1000371], :neighborhood/name "Madrona"}
{:community/category ["neighborhood community site: buying/selling" "restaurants" "favorite places" "etc.  Also community association meeting minutes"], :community/orgtype :community.orgtype/community, :community/type :community.type/wiki, :db/id #db/id[:db.part/user -1000372], :community/name "Madrona Neighborhood", :community/url "http://madrona.wetpaint.com/", :community/neighborhood #db/id[:db.part/user -1000371]}
{:db/id #db/id[:db.part/user -1000373], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000374], :neighborhood/name "Magnolia"}
{:community/category ["planning issues"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000375], :community/name "Magnolia Action Group", :community/url "http://www.orgsites.com/wa/mag/", :community/neighborhood #db/id[:db.part/user -1000374]}
{:db/id #db/id[:db.part/user -1000376], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000377], :neighborhood/name "Magnolia"}
{:community/category ["chamber of commerce"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000378], :community/name "Magnolia Chamber of Commerce", :community/url "http://www.magnoliachamber.org/", :community/neighborhood #db/id[:db.part/user -1000377]}
{:db/id #db/id[:db.part/user -1000379], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000380], :neighborhood/name "Magnolia"}
{:community/category ["historical society"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000381], :community/name "Magnolia Historical Society", :community/url "http://www.magnoliahistoricalsociety.org/", :community/neighborhood #db/id[:db.part/user -1000380]}
{:db/id #db/id[:db.part/user -1000382], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000383], :neighborhood/name "Magnolia"}
{:community/category ["planning issues"], :community/orgtype :community.orgtype/community, :community/type :community.type/wiki, :db/id #db/id[:db.part/user -1000384], :community/name "Magnolia is Really Part of Seattle", :community/url "http://sleeplessinmagnolia.ning.com/", :community/neighborhood #db/id[:db.part/user -1000383]}
{:db/id #db/id[:db.part/user -1000385], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000386], :neighborhood/name "Magnolia"}
{:community/category ["neighborhood association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000387], :community/name "Magnolia Neighborhood Planning Council", :community/url "http://magnolianpc.com/", :community/neighborhood #db/id[:db.part/user -1000386]}
{:db/id #db/id[:db.part/user -1000388], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000389], :neighborhood/name "Magnolia"}
{:community/category ["news" "events" "criminal activity" "food"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/twitter, :db/id #db/id[:db.part/user -1000390], :community/name "Magnolia Voice", :community/url "http://twitter.com/magnoliavoice", :community/neighborhood #db/id[:db.part/user -1000389]}
{:db/id #db/id[:db.part/user -1000391], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000392], :neighborhood/name "Magnolia"}
{:community/category ["news" "events" "criminal activity" "food"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/facebook-page, :db/id #db/id[:db.part/user -1000393], :community/name "Magnolia Voice", :community/url "http://www.facebook.com/pages/Magnolia-Voice-Seattle/116057104388", :community/neighborhood #db/id[:db.part/user -1000392]}
{:db/id #db/id[:db.part/user -1000394], :district/name "Magnolia/Queen Anne"}
{:db/id #db/id[:db.part/user -1000395], :neighborhood/name "Magnolia"}
{:community/category ["news" "events" "criminal activity" "food"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000396], :community/name "Magnolia Voice", :community/url "http://www.magnoliavoice.com/", :community/neighborhood #db/id[:db.part/user -1000395]}
{:db/id #db/id[:db.part/user -1000397], :district/name "Northeast"}
{:db/id #db/id[:db.part/user -1000398], :neighborhood/name "Laurelhurst"}
{:community/category ["garden"], :community/type :community.type/website, :db/id #db/id[:db.part/user -1000399], :community/name "Magnuson Community Garden", :community/url "http://cityofseattle.net/MAGNUSONGARDEN", :community/neighborhood #db/id[:db.part/user -1000398]}
{:db/id #db/id[:db.part/user -1000400], :district/name "Northeast"}
{:db/id #db/id[:db.part/user -1000401], :neighborhood/name "Laurelhurst"}
{:community/category ["park issues"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000402], :community/name "Magnuson Environmental Stewardship Alliance", :community/url "http://mesaseattle.org/", :community/neighborhood #db/id[:db.part/user -1000401]}
{:db/id #db/id[:db.part/user -1000403], :district/name "Northeast"}
{:db/id #db/id[:db.part/user -1000404], :neighborhood/name "Maple Leaf", :neighborhood/district #db/id[:db.part/user -1000403]}
{:community/category ["community council" "email list available"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000405], :community/name "Maple Leaf Community Council", :community/url "http://www.mapleleafcommunity.org/", :community/neighborhood #db/id[:db.part/user -1000404]}
{:db/id #db/id[:db.part/user -1000406], :district/name "North"}
{:db/id #db/id[:db.part/user -1000407], :neighborhood/name "Maple Leaf"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/twitter, :db/id #db/id[:db.part/user -1000408], :community/name "Maple Leaf Life", :community/url "http://twitter.com/mapleleaflife", :community/neighborhood #db/id[:db.part/user -1000407]}
{:db/id #db/id[:db.part/user -1000409], :district/name "North"}
{:db/id #db/id[:db.part/user -1000410], :neighborhood/name "Maple Leaf"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/facebook-page, :db/id #db/id[:db.part/user -1000411], :community/name "Maple Leaf Life", :community/url "http://www.facebook.com/pages/Seattle-WA/Maple-Leaf-Life/298056021657", :community/neighborhood #db/id[:db.part/user -1000410]}
{:db/id #db/id[:db.part/user -1000412], :district/name "North"}
{:db/id #db/id[:db.part/user -1000413], :neighborhood/name "Maple Leaf"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000414], :community/name "Maple Leaf Life", :community/url "http://www.mapleleaflife.com/", :community/neighborhood #db/id[:db.part/user -1000413]}
{:db/id #db/id[:db.part/user -1000415], :district/name "Greater Duwamish"}
{:db/id #db/id[:db.part/user -1000416], :neighborhood/name "South Park"}
{:community/category ["farmers' market"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000417], :community/name "Market On Wheels", :community/url "http://marketonwheels.wordpress.com/", :community/neighborhood #db/id[:db.part/user -1000416]}
{:db/id #db/id[:db.part/user -1000418], :district/name "East"}
{:db/id #db/id[:db.part/user -1000419], :neighborhood/name "Miller Park", :neighborhood/district #db/id[:db.part/user -1000418]}
{:community/category ["neighborhood association; news" "events"], :community/orgtype :community.orgtype/community, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000420], :community/name "Miller Park Neighborhood Association", :community/url "http://millerparkseattle.blogspot.com/", :community/neighborhood #db/id[:db.part/user -1000419]}
{:db/id #db/id[:db.part/user -1000421], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000422], :neighborhood/name "Rainier Valley"}
{:community/category ["business association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000423], :community/name "MLK Business Association", :community/url "http://www.mlkba.org/", :community/neighborhood #db/id[:db.part/user -1000422]}
{:db/id #db/id[:db.part/user -1000424], :district/name "Southwest"}
{:db/id #db/id[:db.part/user -1000425], :neighborhood/name "Morgan Junction", :neighborhood/district #db/id[:db.part/user -1000424]}
{:community/category ["community association"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000426], :community/name "Morgan Junction Community Association", :community/url "http://morganjunction.org/", :community/neighborhood #db/id[:db.part/user -1000425]}
{:db/id #db/id[:db.part/user -1000427], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000428], :neighborhood/name "Mount Baker", :neighborhood/district #db/id[:db.part/user -1000427]}
{:community/category ["community club"], :community/type :community.type/email-list, :db/id #db/id[:db.part/user -1000429], :community/name "Mount Baker Community Club", :community/url "http://groups.yahoo.com/group/MBCCCommunityNotices/", :community/neighborhood #db/id[:db.part/user -1000428]}
{:db/id #db/id[:db.part/user -1000430], :district/name "Southeast"}
{:db/id #db/id[:db.part/user -1000431], :neighborhood/name "Mount Baker"}
{:community/category ["community group"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000432], :community/name "Mount Baker Neighborhood ", :community/url "http://www.mountbaker.org/index.php", :community/neighborhood #db/id[:db.part/user -1000431]}
{:db/id #db/id[:db.part/user -1000433], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000434], :neighborhood/name "Green Lake"}
{:community/category ["news" "events" "food"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000435], :community/name "My Greenlake Blog", :community/url "http://www.mygreenlake.com/", :community/neighborhood #db/id[:db.part/user -1000434]}
{:db/id #db/id[:db.part/user -1000436], :district/name "Ballard"}
{:db/id #db/id[:db.part/user -1000437], :neighborhood/name "Ballard"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000438], :community/name "MyBallard", :community/url "http://www.myballard.com/", :community/neighborhood #db/id[:db.part/user -1000437]}
{:db/id #db/id[:db.part/user -1000439], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000440], :neighborhood/name "Wallingford"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/blog, :db/id #db/id[:db.part/user -1000441], :community/name "MyWallingford", :community/url "http://mywallingford.com", :community/neighborhood #db/id[:db.part/user -1000440]}
{:db/id #db/id[:db.part/user -1000442], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000443], :neighborhood/name "Wallingford"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/twitter, :db/id #db/id[:db.part/user -1000444], :community/name "MyWallingford", :community/url "http://twitter.com/mywallingford", :community/neighborhood #db/id[:db.part/user -1000443]}
{:db/id #db/id[:db.part/user -1000445], :district/name "Northwest"}
{:db/id #db/id[:db.part/user -1000446], :neighborhood/name "Wallingford"}
{:community/category ["news" "events" "human interest" "criminal activity" "shopping"], :community/orgtype :community.orgtype/commercial, :community/type :community.type/facebook-page, :db/id #db/id[:db.part/user -1000447], :community/name "MyWallingford", :community/url "http://www.facebook.com/MyWallingford?ref=ts", :community/neighborhood #db/id[:db.part/user -1000446]}
{:db/id #db/id[:db.part/user -1000448], :district/name "Delridge"}
{:db/id #db/id[:db.part/user -1000449], :neighborhood/name "Delridge"}
{:community/category ["environmental conservation"], :community/orgtype :community.orgtype/community, :community/type :community.type/website, :db/id #db/id[:db.part/user -1000450], :community/name "Nature Consortium", :community/url "http://www.naturec.org", :community/neighborhood #db/id[:db.part/user -1000449]}
]
