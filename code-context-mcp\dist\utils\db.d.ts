export declare const SCHEMA_SQL = "\nCREATE TABLE IF NOT EXISTS repository (\n  id INTEGER PRIMARY KEY AUTOINCREMENT,\n  name TEXT NOT NULL,\n  path TEXT NOT NULL,\n  last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  UNIQUE(path)\n);\n\nCREATE TABLE IF NOT EXISTS branch (\n  id INTEGER PRIMARY KEY AUTOINCREMENT,\n  name TEXT NOT NULL,\n  repository_id INTEGER NOT NULL,\n  last_commit_sha TEXT NOT NULL,\n  status TEXT CHECK(status IN ('pending', 'files_processed', 'embeddings_generated')) DEFAULT 'pending',\n  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  FOREIGN KEY (repository_id) REFERENCES repository(id) ON DELETE CASCADE,\n  UNIQUE(name, repository_id)\n);\n\nCREATE TABLE IF NOT EXISTS file (\n  id INTEGER PRIMARY KEY AUTOINCREMENT,\n  repository_id INTEGER NOT NULL,\n  path TEXT NOT NULL,\n  name TEXT NOT NULL,\n  sha TEXT NOT NULL,\n  status TEXT CHECK(status IN ('pending', 'fetched', 'ingested', 'done')) DEFAULT 'pending',\n  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  FOREIGN KEY (repository_id) REFERENCES repository(id) ON DELETE CASCADE,\n  UNIQUE(repository_id, path, sha)\n);\n\nCREATE TABLE IF NOT EXISTS branch_file_association (\n  branch_id INTEGER NOT NULL,\n  file_id INTEGER NOT NULL,\n  PRIMARY KEY (branch_id, file_id),\n  FOREIGN KEY (branch_id) REFERENCES branch(id) ON DELETE CASCADE,\n  FOREIGN KEY (file_id) REFERENCES file(id) ON DELETE CASCADE\n);\n\nCREATE TABLE IF NOT EXISTS file_chunk (\n  id INTEGER PRIMARY KEY AUTOINCREMENT,\n  file_id INTEGER NOT NULL,\n  content TEXT NOT NULL,\n  chunk_number INTEGER NOT NULL,\n  embedding TEXT,\n  model_version TEXT,\n  token_count INTEGER,\n  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  FOREIGN KEY (file_id) REFERENCES file(id) ON DELETE CASCADE,\n  UNIQUE(file_id, chunk_number)\n);\n";
export declare const initializeDatabase: () => void;
export interface DatabaseOperations {
    prepare: (sql: string) => {
        run: (params?: any) => any;
        get: (params?: any) => any;
        all: (params?: any) => any;
    };
}
export interface DatabaseInterface {
    run: (sql: string, params?: any) => any;
    get: (sql: string, params?: any) => any;
    all: (sql: string, params?: any) => any;
    transaction: (cb: (dbOps: any) => any) => any;
    close: () => void;
}
declare const dbInterface: DatabaseInterface;
export default dbInterface;
